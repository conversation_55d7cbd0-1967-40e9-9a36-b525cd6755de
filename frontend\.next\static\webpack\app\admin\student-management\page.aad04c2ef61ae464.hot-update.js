"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/student-management/page",{

/***/ "(app-pages-browser)/./src/app/admin/student-management/page.jsx":
/*!***************************************************!*\
  !*** ./src/app/admin/student-management/page.jsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StudentManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_GraduationCap_RefreshCw_Save_Search_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,GraduationCap,RefreshCw,Save,Search,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_GraduationCap_RefreshCw_Save_Search_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,GraduationCap,RefreshCw,Save,Search,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _api_students__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../api/students */ \"(app-pages-browser)/./src/api/students.js\");\n/* harmony import */ var _api_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../api/auth */ \"(app-pages-browser)/./src/api/auth.js\");\n/* harmony import */ var _StudentDropdown__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./StudentDropdown */ \"(app-pages-browser)/./src/app/admin/student-management/StudentDropdown.jsx\");\n/* harmony import */ var _StudentProfile__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./StudentProfile */ \"(app-pages-browser)/./src/app/admin/student-management/StudentProfile.jsx\");\n/* harmony import */ var _DepartmentCards__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./DepartmentCards */ \"(app-pages-browser)/./src/app/admin/student-management/DepartmentCards.jsx\");\n/* harmony import */ var _PassoutYearCards__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./PassoutYearCards */ \"(app-pages-browser)/./src/app/admin/student-management/PassoutYearCards.jsx\");\n/* harmony import */ var _StudentList__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./StudentList */ \"(app-pages-browser)/./src/app/admin/student-management/StudentList.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction StudentManagement() {\n    var _departmentOptions_find, _departmentOptions_find1;\n    _s();\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [debouncedSearchTerm, setDebouncedSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedDepartment, setSelectedDepartment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedYear, setSelectedYear] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [selectedStudent, setSelectedStudent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isEditing, setIsEditing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editedStudent, setEditedStudent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isYearDropdownOpen, setIsYearDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allStudents, setAllStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isRetrying, setIsRetrying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalStudents, setTotalStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [availableYears, setAvailableYears] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [departmentStats, setDepartmentStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedPassoutYear, setSelectedPassoutYear] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [cgpaMin, setCgpaMin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [cgpaMax, setCgpaMax] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Dropdown options\n    const departmentOptions = [\n        {\n            value: 'Computer Science',\n            label: 'Computer Science'\n        },\n        {\n            value: 'Electronics',\n            label: 'Electronics'\n        },\n        {\n            value: 'Mechanical',\n            label: 'Mechanical'\n        },\n        {\n            value: 'Civil',\n            label: 'Civil'\n        },\n        {\n            value: 'Electrical',\n            label: 'Electrical'\n        },\n        {\n            value: 'Information Technology',\n            label: 'Information Technology'\n        },\n        {\n            value: 'Chemical',\n            label: 'Chemical'\n        },\n        {\n            value: 'Biotechnology',\n            label: 'Biotechnology'\n        }\n    ];\n    // Fetch all students for complete dataset\n    const fetchAllStudents = async ()=>{\n        try {\n            const token = (0,_api_auth__WEBPACK_IMPORTED_MODULE_3__.getAuthToken)();\n            if (!token) {\n                throw new Error('No authentication token found. Please login first.');\n            }\n            let allData = [];\n            let page = 1;\n            let hasMore = true;\n            while(hasMore){\n                const response = await _api_students__WEBPACK_IMPORTED_MODULE_2__.studentsAPI.getStudents({\n                    page,\n                    page_size: 100\n                });\n                allData = [\n                    ...allData,\n                    ...response.data\n                ];\n                if (response.pagination) {\n                    hasMore = page < response.pagination.total_pages;\n                    page++;\n                } else {\n                    hasMore = false;\n                }\n            }\n            const studentsData = allData.map((student)=>({\n                    id: student.id,\n                    rollNumber: student.student_id || 'N/A',\n                    name: \"\".concat(student.first_name || '', \" \").concat(student.last_name || '').trim() || 'Unknown',\n                    email: student.contact_email || student.email || 'N/A',\n                    phone: student.phone || 'N/A',\n                    department: student.branch || 'N/A',\n                    year: getYearFromBranch(student.branch, student),\n                    cgpa: student.gpa || 'N/A',\n                    address: student.address || 'N/A',\n                    dateOfBirth: student.date_of_birth || '',\n                    parentContact: student.parent_contact || 'N/A',\n                    education: student.education || 'N/A',\n                    skills: student.skills || [],\n                    // Academic details\n                    joining_year: student.joining_year || student.admission_year || '',\n                    passout_year: student.passout_year || student.graduation_year || '',\n                    // Class XII details\n                    twelfth_cgpa: student.twelfth_cgpa || student.class_12_cgpa || '',\n                    twelfth_percentage: student.twelfth_percentage || student.class_12_percentage || '',\n                    twelfth_year_of_passing: student.twelfth_year_of_passing || student.class_12_year || '',\n                    twelfth_school: student.twelfth_school || student.class_12_school || '',\n                    twelfth_board: student.twelfth_board || student.class_12_board || '',\n                    twelfth_location: student.twelfth_location || student.class_12_location || 'Mumbai, Maharashtra',\n                    twelfth_specialization: student.twelfth_specialization || student.class_12_stream || 'Science',\n                    // Class X details\n                    tenth_cgpa: student.tenth_cgpa || student.class_10_cgpa || '',\n                    tenth_percentage: student.tenth_percentage || student.class_10_percentage || '',\n                    tenth_year_of_passing: student.tenth_year_of_passing || student.class_10_year || '',\n                    tenth_school: student.tenth_school || student.class_10_school || '',\n                    tenth_board: student.tenth_board || student.class_10_board || '',\n                    tenth_location: student.tenth_location || student.class_10_location || 'Mumbai, Maharashtra',\n                    tenth_specialization: student.tenth_specialization || student.class_10_stream || 'General',\n                    // Address details\n                    city: student.city || '',\n                    district: student.district || '',\n                    state: student.state || '',\n                    pincode: student.pincode || student.pin_code || '',\n                    country: student.country || 'India',\n                    // Certificate URLs\n                    tenth_certificate: student.tenth_certificate || student.class_10_certificate || '',\n                    twelfth_certificate: student.twelfth_certificate || student.class_12_certificate || '',\n                    tenth_certificate_url: student.tenth_certificate_url || student.class_10_certificate_url || '',\n                    twelfth_certificate_url: student.twelfth_certificate_url || student.class_12_certificate_url || '',\n                    // Resume details\n                    resume: student.resume || '',\n                    resume_url: student.resume_url || '',\n                    // Semester-wise CGPA data\n                    semester_cgpas: student.semester_cgpas || [\n                        {\n                            semester: 1,\n                            cgpa: 8.2\n                        },\n                        {\n                            semester: 2,\n                            cgpa: 8.5\n                        },\n                        {\n                            semester: 3,\n                            cgpa: 8.7\n                        },\n                        {\n                            semester: 4,\n                            cgpa: 8.4\n                        },\n                        {\n                            semester: 5,\n                            cgpa: 8.6\n                        },\n                        {\n                            semester: 6,\n                            cgpa: 8.8\n                        },\n                        {\n                            semester: 7,\n                            cgpa: 8.3\n                        },\n                        {\n                            semester: 8,\n                            cgpa: 8.5\n                        }\n                    ],\n                    // Add some mock data if fields are empty (for testing purposes)\n                    ...!student.city && !student.twelfth_cgpa ? {\n                        city: 'Mumbai',\n                        district: 'Mumbai',\n                        state: 'Maharashtra',\n                        pincode: '400001',\n                        country: 'India',\n                        joining_year: '2020',\n                        passout_year: '2024',\n                        twelfth_cgpa: '9.2',\n                        twelfth_percentage: '87.4%',\n                        twelfth_year_of_passing: '2020',\n                        twelfth_school: 'ABC Junior College',\n                        twelfth_board: 'Maharashtra State Board',\n                        twelfth_location: 'Mumbai, Maharashtra',\n                        twelfth_specialization: 'Science',\n                        tenth_cgpa: '9.0',\n                        tenth_percentage: '85.6%',\n                        tenth_year_of_passing: '2018',\n                        tenth_school: 'XYZ High School',\n                        tenth_board: 'Maharashtra State Board',\n                        tenth_location: 'Mumbai, Maharashtra',\n                        tenth_specialization: 'General'\n                    } : {}\n                }));\n            setAllStudents(studentsData);\n            setAvailableYears(getAvailableYears(studentsData));\n            setDepartmentStats(getDepartmentStats(studentsData));\n            return studentsData;\n        } catch (err) {\n            console.error('Error fetching all students:', err);\n            throw err;\n        }\n    };\n    // Debounce search term\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentManagement.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"StudentManagement.useEffect.timer\": ()=>{\n                    setDebouncedSearchTerm(searchTerm);\n                }\n            }[\"StudentManagement.useEffect.timer\"], 300); // 300ms delay\n            return ({\n                \"StudentManagement.useEffect\": ()=>clearTimeout(timer)\n            })[\"StudentManagement.useEffect\"];\n        }\n    }[\"StudentManagement.useEffect\"], [\n        searchTerm\n    ]);\n    // Fetch students from Django backend with pagination\n    const fetchStudents = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1;\n        try {\n            setLoading(true);\n            setError(null);\n            setIsRetrying(false);\n            const token = (0,_api_auth__WEBPACK_IMPORTED_MODULE_3__.getAuthToken)();\n            if (!token) {\n                throw new Error('No authentication token found. Please login first.');\n            }\n            // Use allStudents if already loaded, otherwise fetch all\n            let allData = allStudents;\n            if (allStudents.length === 0) {\n                allData = await fetchAllStudents();\n            }\n            // Apply filters to get the filtered dataset\n            let filteredData = allData;\n            if (selectedDepartment) {\n                filteredData = filteredData.filter((student)=>student.department === selectedDepartment);\n            }\n            if (selectedYear !== 'all') {\n                filteredData = filteredData.filter((student)=>student.year === selectedYear);\n            }\n            if (debouncedSearchTerm) {\n                const searchLower = debouncedSearchTerm.toLowerCase();\n                filteredData = filteredData.filter((student)=>student.name.toLowerCase().includes(searchLower) || student.rollNumber.toLowerCase().includes(searchLower));\n            }\n            // Implement client-side pagination\n            const studentsPerPage = 10;\n            const startIndex = (page - 1) * studentsPerPage;\n            const endIndex = startIndex + studentsPerPage;\n            const paginatedStudents = filteredData.slice(startIndex, endIndex);\n            setStudents(paginatedStudents);\n            setCurrentPage(page);\n            setTotalPages(Math.ceil(filteredData.length / studentsPerPage));\n            setTotalStudents(filteredData.length);\n            setLoading(false);\n        } catch (err) {\n            var _err_response, _err_response1;\n            console.error('Error fetching students:', err);\n            if (((_err_response = err.response) === null || _err_response === void 0 ? void 0 : _err_response.status) === 401) {\n                setError('Authentication failed. Please login again.');\n            } else if (((_err_response1 = err.response) === null || _err_response1 === void 0 ? void 0 : _err_response1.status) === 403) {\n                setError('You do not have permission to view students. Admin access required.');\n            } else if (err.message.includes('token')) {\n                setError('Please login to access student management.');\n            } else {\n                setError(\"Error: \".concat(err.message));\n            }\n            setStudents([]);\n            setLoading(false);\n        }\n    };\n    // Helper function to determine year from branch (you can customize this logic)\n    const getYearFromBranch = (branch, student)=>{\n        if (student && student.joining_year && student.passout_year) {\n            return \"\".concat(student.joining_year, \"-\").concat(student.passout_year);\n        }\n        return 'N/A';\n    };\n    // Initial data fetch\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentManagement.useEffect\": ()=>{\n            fetchStudents();\n        }\n    }[\"StudentManagement.useEffect\"], []);\n    // Add this useEffect after your existing useEffect\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentManagement.useEffect\": ()=>{\n            // Check if user is authenticated\n            const token = (0,_api_auth__WEBPACK_IMPORTED_MODULE_3__.getAuthToken)();\n            if (!token) {\n                // Redirect to login page or show login prompt\n                setError('Please login to access student management.');\n                setLoading(false);\n                return;\n            }\n            fetchStudents();\n        }\n    }[\"StudentManagement.useEffect\"], []);\n    // Helper function to extract year from student ID (assuming format like CS2021001)\n    const getYearFromStudentId = (studentId)=>{\n        if (studentId && studentId.length >= 6) {\n            const yearPart = studentId.substring(2, 6);\n            if (!isNaN(yearPart)) {\n                return \"\".concat(4 - (new Date().getFullYear() - parseInt(yearPart)), \"th Year\");\n            }\n        }\n        return 'Unknown';\n    };\n    // Close dropdown when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentManagement.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"StudentManagement.useEffect.handleClickOutside\": (event)=>{\n                    if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                        setIsYearDropdownOpen(false);\n                    }\n                }\n            }[\"StudentManagement.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"StudentManagement.useEffect\": ()=>{\n                    document.removeEventListener('mousedown', handleClickOutside);\n                }\n            })[\"StudentManagement.useEffect\"];\n        }\n    }[\"StudentManagement.useEffect\"], []);\n    // Get available years from students data\n    const getAvailableYears = (studentsData)=>{\n        const years = [\n            ...new Set(studentsData.map((student)=>student.year).filter((year)=>year && year !== 'N/A'))\n        ];\n        return years.sort();\n    };\n    // Get department statistics\n    const getDepartmentStats = (studentsData)=>{\n        const stats = {};\n        studentsData.forEach((student)=>{\n            if (student.department && student.department !== 'N/A') {\n                stats[student.department] = (stats[student.department] || 0) + 1;\n            }\n        });\n        return Object.entries(stats).map((param)=>{\n            let [department, count] = param;\n            return {\n                department,\n                count\n            };\n        });\n    };\n    // Get available passout years for selected department\n    const getAvailablePassoutYears = ()=>{\n        if (!selectedDepartment) return [];\n        const years = allStudents.filter((s)=>s.department === selectedDepartment && s.year && s.year !== 'N/A').map((s)=>{\n            // Extract passout year from year string (format: \"joining-passout\")\n            const parts = s.year.split('-');\n            return parts.length === 2 ? parts[1] : null;\n        }).filter((y)=>y).map(Number).filter((y)=>!isNaN(y));\n        // Unique and descending\n        return Array.from(new Set(years)).sort((a, b)=>b - a);\n    };\n    // Filter students for selected department and passout year\n    const getFilteredStudents = ()=>{\n        let filtered = allStudents;\n        if (selectedDepartment) {\n            filtered = filtered.filter((s)=>s.department === selectedDepartment);\n        }\n        if (selectedPassoutYear) {\n            filtered = filtered.filter((s)=>{\n                if (!s.year || s.year === 'N/A') return false;\n                const parts = s.year.split('-');\n                return parts.length === 2 && parts[1] === String(selectedPassoutYear);\n            });\n        }\n        if (debouncedSearchTerm) {\n            const searchLower = debouncedSearchTerm.toLowerCase();\n            filtered = filtered.filter((student)=>student.name.toLowerCase().includes(searchLower) || student.rollNumber.toLowerCase().includes(searchLower));\n        }\n        // CGPA filter\n        filtered = filtered.filter((student)=>{\n            const cgpa = parseFloat(student.cgpa);\n            if (cgpaMin && (isNaN(cgpa) || cgpa < parseFloat(cgpaMin))) return false;\n            if (cgpaMax && (isNaN(cgpa) || cgpa > parseFloat(cgpaMax))) return false;\n            return true;\n        });\n        return filtered;\n    };\n    // Update filters and refetch when dependencies change (but not searchTerm)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentManagement.useEffect\": ()=>{\n            if (allStudents.length > 0) {\n                fetchStudents(1); // Reset to page 1 when filters change\n            }\n        }\n    }[\"StudentManagement.useEffect\"], [\n        selectedDepartment,\n        selectedYear,\n        debouncedSearchTerm,\n        selectedPassoutYear\n    ]);\n    // Filter students based on selected department, year, and search term\n    const filteredStudents = students; // Students are already filtered in fetchStudents\n    const handleStudentClick = (student)=>{\n        setSelectedStudent(student);\n        setEditedStudent({\n            ...student\n        });\n        setIsEditing(false);\n    };\n    const handleBackToList = ()=>{\n        setSelectedStudent(null);\n        setIsEditing(false);\n        setEditedStudent(null);\n    };\n    const handleBackToDepartments = ()=>{\n        setSelectedDepartment(null);\n        setSelectedYear('all');\n        setSearchTerm('');\n    };\n    const handleEdit = ()=>{\n        setIsEditing(true);\n    };\n    const handleSave = ()=>{\n        // Update the student in the list\n        setStudents((prev)=>prev.map((student)=>student.id === editedStudent.id ? editedStudent : student));\n        setSelectedStudent(editedStudent);\n        setIsEditing(false);\n    // Here you would typically make an API call to save the changes\n    };\n    const handleCancel = ()=>{\n        setEditedStudent({\n            ...selectedStudent\n        });\n        setIsEditing(false);\n    };\n    const handleInputChange = (field, value)=>{\n        setEditedStudent((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    // Handle retry button click\n    const handleRetry = ()=>{\n        setIsRetrying(true);\n        fetchStudents();\n    };\n    // Help developers find the correct API endpoint\n    const debugBackend = ()=>{\n        window.open('http://localhost:8000/admin/');\n    };\n    const handleSearch = ()=>{\n        // Force immediate search without waiting for debounce\n        setDebouncedSearchTerm(searchTerm);\n        setCurrentPage(1);\n    };\n    // Handle pagination\n    const handlePageChange = (newPage)=>{\n        if (newPage >= 1 && newPage <= totalPages) {\n            fetchStudents(newPage);\n        }\n    };\n    // Handle search input change\n    const handleSearchInputChange = (e)=>{\n        setSearchTerm(e.target.value);\n    // Don't trigger immediate search, let debounce handle it\n    };\n    // Handle search input key press\n    const handleSearchKeyDown = (e)=>{\n        if (e.key === 'Enter') {\n            e.preventDefault();\n            handleSearch();\n        }\n    };\n    if (loading) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 p-6 ml-20 overflow-y-auto h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center h-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mb-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                    lineNumber: 474,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-gray-600\",\n                    children: \"Loading students...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                    lineNumber: 475,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n            lineNumber: 473,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n        lineNumber: 472,\n        columnNumber: 5\n    }, this);\n    if (error && students.length === 0) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 p-6 ml-20 overflow-y-auto h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center h-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-red-500 mb-4 text-center max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-semibold text-lg mb-2\",\n                            children: \"Access Error\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                            lineNumber: 484,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                            lineNumber: 485,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 text-sm text-gray-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Possible solutions:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                    lineNumber: 487,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"list-disc list-inside mt-2 text-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Make sure you're logged in with admin credentials\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                            lineNumber: 489,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Check if your session has expired\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                            lineNumber: 490,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Verify Django server is running on port 8000\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                            lineNumber: 491,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Ensure proper permissions are set in Django\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                            lineNumber: 492,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                    lineNumber: 488,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                            lineNumber: 486,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                    lineNumber: 483,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-3 mt-4\",\n                    children: [\n                        !error.includes('login') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleRetry,\n                            className: \"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                            disabled: isRetrying,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_GraduationCap_RefreshCw_Save_Search_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-4 h-4 \".concat(isRetrying ? 'animate-spin' : '')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                    lineNumber: 503,\n                                    columnNumber: 15\n                                }, this),\n                                isRetrying ? 'Retrying...' : 'Retry'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                            lineNumber: 498,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>window.location.href = '/login',\n                            className: \"flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_GraduationCap_RefreshCw_Save_Search_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                    lineNumber: 511,\n                                    columnNumber: 13\n                                }, this),\n                                \"Go to Login\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                            lineNumber: 507,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                    lineNumber: 496,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n            lineNumber: 482,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n        lineNumber: 481,\n        columnNumber: 5\n    }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 p-6 ml-20 overflow-y-auto h-full\",\n        children: !selectedStudent ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: !selectedDepartment ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DepartmentCards__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                departmentOptions: departmentOptions,\n                departmentStats: departmentStats,\n                allStudents: allStudents,\n                onSelect: setSelectedDepartment\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                lineNumber: 524,\n                columnNumber: 13\n            }, this) : !selectedPassoutYear ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PassoutYearCards__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                departmentLabel: (_departmentOptions_find = departmentOptions.find((d)=>d.value === selectedDepartment)) === null || _departmentOptions_find === void 0 ? void 0 : _departmentOptions_find.label,\n                onBack: handleBackToDepartments,\n                getAvailablePassoutYears: getAvailablePassoutYears,\n                allStudents: allStudents,\n                selectedDepartment: selectedDepartment,\n                onSelectYear: setSelectedPassoutYear\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                lineNumber: 531,\n                columnNumber: 13\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StudentList__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                departmentLabel: (_departmentOptions_find1 = departmentOptions.find((d)=>d.value === selectedDepartment)) === null || _departmentOptions_find1 === void 0 ? void 0 : _departmentOptions_find1.label,\n                passoutYear: selectedPassoutYear,\n                onBack: ()=>setSelectedPassoutYear(null),\n                searchTerm: searchTerm,\n                handleSearchInputChange: handleSearchInputChange,\n                handleSearchKeyDown: handleSearchKeyDown,\n                cgpaMin: cgpaMin,\n                setCgpaMin: setCgpaMin,\n                cgpaMax: cgpaMax,\n                setCgpaMax: setCgpaMax,\n                handleSearch: handleSearch,\n                getFilteredStudents: getFilteredStudents,\n                currentPage: currentPage,\n                handlePageChange: handlePageChange,\n                handleStudentClick: handleStudentClick,\n                loading: loading\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                lineNumber: 540,\n                columnNumber: 13\n            }, this)\n        }, void 0, false) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StudentProfile__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            selectedStudent: selectedStudent,\n            editedStudent: editedStudent,\n            isEditing: isEditing,\n            handleBackToList: handleBackToList,\n            handleEdit: handleEdit,\n            handleSave: handleSave,\n            handleCancel: handleCancel,\n            handleInputChange: handleInputChange,\n            departmentOptions: departmentOptions\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n            lineNumber: 561,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n        lineNumber: 520,\n        columnNumber: 5\n    }, this);\n}\n_s(StudentManagement, \"vV3ywjzqfJaaIG37j/kyv5UkL2w=\");\n_c = StudentManagement;\nvar _c;\n$RefreshReg$(_c, \"StudentManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/student-management/page.jsx\n"));

/***/ })

});