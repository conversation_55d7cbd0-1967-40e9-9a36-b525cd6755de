"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/student-management/page",{

/***/ "(app-pages-browser)/./src/app/admin/student-management/page.jsx":
/*!***************************************************!*\
  !*** ./src/app/admin/student-management/page.jsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StudentManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_GraduationCap_RefreshCw_Save_Search_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,GraduationCap,RefreshCw,Save,Search,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_GraduationCap_RefreshCw_Save_Search_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,GraduationCap,RefreshCw,Save,Search,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _api_students__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../api/students */ \"(app-pages-browser)/./src/api/students.js\");\n/* harmony import */ var _api_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../api/auth */ \"(app-pages-browser)/./src/api/auth.js\");\n/* harmony import */ var _StudentDropdown__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./StudentDropdown */ \"(app-pages-browser)/./src/app/admin/student-management/StudentDropdown.jsx\");\n/* harmony import */ var _StudentProfile__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./StudentProfile */ \"(app-pages-browser)/./src/app/admin/student-management/StudentProfile.jsx\");\n/* harmony import */ var _DepartmentCards__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./DepartmentCards */ \"(app-pages-browser)/./src/app/admin/student-management/DepartmentCards.jsx\");\n/* harmony import */ var _PassoutYearCards__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./PassoutYearCards */ \"(app-pages-browser)/./src/app/admin/student-management/PassoutYearCards.jsx\");\n/* harmony import */ var _StudentList__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./StudentList */ \"(app-pages-browser)/./src/app/admin/student-management/StudentList.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction StudentManagement() {\n    var _departmentOptions_find, _departmentOptions_find1;\n    _s();\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [debouncedSearchTerm, setDebouncedSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedDepartment, setSelectedDepartment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedYear, setSelectedYear] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [selectedStudent, setSelectedStudent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isEditing, setIsEditing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editedStudent, setEditedStudent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isYearDropdownOpen, setIsYearDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allStudents, setAllStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isRetrying, setIsRetrying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalStudents, setTotalStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [availableYears, setAvailableYears] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [departmentStats, setDepartmentStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedPassoutYear, setSelectedPassoutYear] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [cgpaMin, setCgpaMin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [cgpaMax, setCgpaMax] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Dropdown options\n    const departmentOptions = [\n        {\n            value: 'Computer Science',\n            label: 'Computer Science'\n        },\n        {\n            value: 'Electronics',\n            label: 'Electronics'\n        },\n        {\n            value: 'Mechanical',\n            label: 'Mechanical'\n        },\n        {\n            value: 'Civil',\n            label: 'Civil'\n        },\n        {\n            value: 'Electrical',\n            label: 'Electrical'\n        },\n        {\n            value: 'Information Technology',\n            label: 'Information Technology'\n        },\n        {\n            value: 'Chemical',\n            label: 'Chemical'\n        },\n        {\n            value: 'Biotechnology',\n            label: 'Biotechnology'\n        }\n    ];\n    // Fetch all students for complete dataset\n    const fetchAllStudents = async ()=>{\n        try {\n            const token = (0,_api_auth__WEBPACK_IMPORTED_MODULE_3__.getAuthToken)();\n            if (!token) {\n                throw new Error('No authentication token found. Please login first.');\n            }\n            let allData = [];\n            let page = 1;\n            let hasMore = true;\n            while(hasMore){\n                const response = await _api_students__WEBPACK_IMPORTED_MODULE_2__.studentsAPI.getStudents({\n                    page,\n                    page_size: 100\n                });\n                allData = [\n                    ...allData,\n                    ...response.data\n                ];\n                if (response.pagination) {\n                    hasMore = page < response.pagination.total_pages;\n                    page++;\n                } else {\n                    hasMore = false;\n                }\n            }\n            // Helper function to get default location based on index\n            const getDefaultLocation = (index)=>{\n                const locations = [\n                    'Mumbai, Maharashtra',\n                    'Delhi, Delhi',\n                    'Bangalore, Karnataka',\n                    'Chennai, Tamil Nadu',\n                    'Pune, Maharashtra',\n                    'Hyderabad, Telangana',\n                    'Kolkata, West Bengal',\n                    'Ahmedabad, Gujarat'\n                ];\n                return locations[index % locations.length];\n            };\n            // Helper function to get default specialization based on index\n            const getDefaultSpecialization = function(index) {\n                let isClass12 = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : true;\n                if (isClass12) {\n                    const specializations = [\n                        'Science',\n                        'Commerce',\n                        'Arts',\n                        'Science',\n                        'Commerce'\n                    ];\n                    return specializations[index % specializations.length];\n                } else {\n                    return 'General';\n                }\n            };\n            const studentsData = allData.map((student, index)=>({\n                    id: student.id,\n                    rollNumber: student.student_id || 'N/A',\n                    name: \"\".concat(student.first_name || '', \" \").concat(student.last_name || '').trim() || 'Unknown',\n                    email: student.contact_email || student.email || 'N/A',\n                    phone: student.phone || 'N/A',\n                    department: student.branch || 'N/A',\n                    year: getYearFromBranch(student.branch, student),\n                    cgpa: student.gpa || 'N/A',\n                    address: student.address || 'N/A',\n                    dateOfBirth: student.date_of_birth || '',\n                    parentContact: student.parent_contact || 'N/A',\n                    education: student.education || 'N/A',\n                    skills: student.skills || [],\n                    // Academic details\n                    joining_year: student.joining_year || student.admission_year || '',\n                    passout_year: student.passout_year || student.graduation_year || '',\n                    // Class XII details\n                    twelfth_cgpa: student.twelfth_cgpa || student.class_12_cgpa || '',\n                    twelfth_percentage: student.twelfth_percentage || student.class_12_percentage || '',\n                    twelfth_year_of_passing: student.twelfth_year_of_passing || student.class_12_year || '',\n                    twelfth_school: student.twelfth_school || student.class_12_school || '',\n                    twelfth_board: student.twelfth_board || student.class_12_board || '',\n                    twelfth_location: student.twelfth_location || student.class_12_location || 'Mumbai, Maharashtra',\n                    twelfth_specialization: student.twelfth_specialization || student.class_12_stream || 'Science',\n                    // Class X details\n                    tenth_cgpa: student.tenth_cgpa || student.class_10_cgpa || '',\n                    tenth_percentage: student.tenth_percentage || student.class_10_percentage || '',\n                    tenth_year_of_passing: student.tenth_year_of_passing || student.class_10_year || '',\n                    tenth_school: student.tenth_school || student.class_10_school || '',\n                    tenth_board: student.tenth_board || student.class_10_board || '',\n                    tenth_location: student.tenth_location || student.class_10_location || 'Mumbai, Maharashtra',\n                    tenth_specialization: student.tenth_specialization || student.class_10_stream || 'General',\n                    // Address details\n                    city: student.city || '',\n                    district: student.district || '',\n                    state: student.state || '',\n                    pincode: student.pincode || student.pin_code || '',\n                    country: student.country || 'India',\n                    // Certificate URLs\n                    tenth_certificate: student.tenth_certificate || student.class_10_certificate || '',\n                    twelfth_certificate: student.twelfth_certificate || student.class_12_certificate || '',\n                    tenth_certificate_url: student.tenth_certificate_url || student.class_10_certificate_url || '',\n                    twelfth_certificate_url: student.twelfth_certificate_url || student.class_12_certificate_url || '',\n                    // Resume details\n                    resume: student.resume || '',\n                    resume_url: student.resume_url || '',\n                    // Semester-wise CGPA data\n                    semester_cgpas: student.semester_cgpas || [\n                        {\n                            semester: 1,\n                            cgpa: 8.2\n                        },\n                        {\n                            semester: 2,\n                            cgpa: 8.5\n                        },\n                        {\n                            semester: 3,\n                            cgpa: 8.7\n                        },\n                        {\n                            semester: 4,\n                            cgpa: 8.4\n                        },\n                        {\n                            semester: 5,\n                            cgpa: 8.6\n                        },\n                        {\n                            semester: 6,\n                            cgpa: 8.8\n                        },\n                        {\n                            semester: 7,\n                            cgpa: 8.3\n                        },\n                        {\n                            semester: 8,\n                            cgpa: 8.5\n                        }\n                    ],\n                    // Add some mock data if fields are empty (for testing purposes)\n                    ...!student.city && !student.twelfth_cgpa ? {\n                        city: 'Mumbai',\n                        district: 'Mumbai',\n                        state: 'Maharashtra',\n                        pincode: '400001',\n                        country: 'India',\n                        joining_year: '2020',\n                        passout_year: '2024',\n                        twelfth_cgpa: '9.2',\n                        twelfth_percentage: '87.4%',\n                        twelfth_year_of_passing: '2020',\n                        twelfth_school: 'ABC Junior College',\n                        twelfth_board: 'Maharashtra State Board',\n                        twelfth_location: 'Mumbai, Maharashtra',\n                        twelfth_specialization: 'Science',\n                        tenth_cgpa: '9.0',\n                        tenth_percentage: '85.6%',\n                        tenth_year_of_passing: '2018',\n                        tenth_school: 'XYZ High School',\n                        tenth_board: 'Maharashtra State Board',\n                        tenth_location: 'Mumbai, Maharashtra',\n                        tenth_specialization: 'General'\n                    } : {}\n                }));\n            setAllStudents(studentsData);\n            setAvailableYears(getAvailableYears(studentsData));\n            setDepartmentStats(getDepartmentStats(studentsData));\n            return studentsData;\n        } catch (err) {\n            console.error('Error fetching all students:', err);\n            throw err;\n        }\n    };\n    // Debounce search term\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentManagement.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"StudentManagement.useEffect.timer\": ()=>{\n                    setDebouncedSearchTerm(searchTerm);\n                }\n            }[\"StudentManagement.useEffect.timer\"], 300); // 300ms delay\n            return ({\n                \"StudentManagement.useEffect\": ()=>clearTimeout(timer)\n            })[\"StudentManagement.useEffect\"];\n        }\n    }[\"StudentManagement.useEffect\"], [\n        searchTerm\n    ]);\n    // Fetch students from Django backend with pagination\n    const fetchStudents = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1;\n        try {\n            setLoading(true);\n            setError(null);\n            setIsRetrying(false);\n            const token = (0,_api_auth__WEBPACK_IMPORTED_MODULE_3__.getAuthToken)();\n            if (!token) {\n                throw new Error('No authentication token found. Please login first.');\n            }\n            // Use allStudents if already loaded, otherwise fetch all\n            let allData = allStudents;\n            if (allStudents.length === 0) {\n                allData = await fetchAllStudents();\n            }\n            // Apply filters to get the filtered dataset\n            let filteredData = allData;\n            if (selectedDepartment) {\n                filteredData = filteredData.filter((student)=>student.department === selectedDepartment);\n            }\n            if (selectedYear !== 'all') {\n                filteredData = filteredData.filter((student)=>student.year === selectedYear);\n            }\n            if (debouncedSearchTerm) {\n                const searchLower = debouncedSearchTerm.toLowerCase();\n                filteredData = filteredData.filter((student)=>student.name.toLowerCase().includes(searchLower) || student.rollNumber.toLowerCase().includes(searchLower));\n            }\n            // Implement client-side pagination\n            const studentsPerPage = 10;\n            const startIndex = (page - 1) * studentsPerPage;\n            const endIndex = startIndex + studentsPerPage;\n            const paginatedStudents = filteredData.slice(startIndex, endIndex);\n            setStudents(paginatedStudents);\n            setCurrentPage(page);\n            setTotalPages(Math.ceil(filteredData.length / studentsPerPage));\n            setTotalStudents(filteredData.length);\n            setLoading(false);\n        } catch (err) {\n            var _err_response, _err_response1;\n            console.error('Error fetching students:', err);\n            if (((_err_response = err.response) === null || _err_response === void 0 ? void 0 : _err_response.status) === 401) {\n                setError('Authentication failed. Please login again.');\n            } else if (((_err_response1 = err.response) === null || _err_response1 === void 0 ? void 0 : _err_response1.status) === 403) {\n                setError('You do not have permission to view students. Admin access required.');\n            } else if (err.message.includes('token')) {\n                setError('Please login to access student management.');\n            } else {\n                setError(\"Error: \".concat(err.message));\n            }\n            setStudents([]);\n            setLoading(false);\n        }\n    };\n    // Helper function to determine year from branch (you can customize this logic)\n    const getYearFromBranch = (branch, student)=>{\n        if (student && student.joining_year && student.passout_year) {\n            return \"\".concat(student.joining_year, \"-\").concat(student.passout_year);\n        }\n        return 'N/A';\n    };\n    // Initial data fetch\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentManagement.useEffect\": ()=>{\n            fetchStudents();\n        }\n    }[\"StudentManagement.useEffect\"], []);\n    // Add this useEffect after your existing useEffect\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentManagement.useEffect\": ()=>{\n            // Check if user is authenticated\n            const token = (0,_api_auth__WEBPACK_IMPORTED_MODULE_3__.getAuthToken)();\n            if (!token) {\n                // Redirect to login page or show login prompt\n                setError('Please login to access student management.');\n                setLoading(false);\n                return;\n            }\n            fetchStudents();\n        }\n    }[\"StudentManagement.useEffect\"], []);\n    // Helper function to extract year from student ID (assuming format like CS2021001)\n    const getYearFromStudentId = (studentId)=>{\n        if (studentId && studentId.length >= 6) {\n            const yearPart = studentId.substring(2, 6);\n            if (!isNaN(yearPart)) {\n                return \"\".concat(4 - (new Date().getFullYear() - parseInt(yearPart)), \"th Year\");\n            }\n        }\n        return 'Unknown';\n    };\n    // Close dropdown when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentManagement.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"StudentManagement.useEffect.handleClickOutside\": (event)=>{\n                    if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                        setIsYearDropdownOpen(false);\n                    }\n                }\n            }[\"StudentManagement.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"StudentManagement.useEffect\": ()=>{\n                    document.removeEventListener('mousedown', handleClickOutside);\n                }\n            })[\"StudentManagement.useEffect\"];\n        }\n    }[\"StudentManagement.useEffect\"], []);\n    // Get available years from students data\n    const getAvailableYears = (studentsData)=>{\n        const years = [\n            ...new Set(studentsData.map((student)=>student.year).filter((year)=>year && year !== 'N/A'))\n        ];\n        return years.sort();\n    };\n    // Get department statistics\n    const getDepartmentStats = (studentsData)=>{\n        const stats = {};\n        studentsData.forEach((student)=>{\n            if (student.department && student.department !== 'N/A') {\n                stats[student.department] = (stats[student.department] || 0) + 1;\n            }\n        });\n        return Object.entries(stats).map((param)=>{\n            let [department, count] = param;\n            return {\n                department,\n                count\n            };\n        });\n    };\n    // Get available passout years for selected department\n    const getAvailablePassoutYears = ()=>{\n        if (!selectedDepartment) return [];\n        const years = allStudents.filter((s)=>s.department === selectedDepartment && s.year && s.year !== 'N/A').map((s)=>{\n            // Extract passout year from year string (format: \"joining-passout\")\n            const parts = s.year.split('-');\n            return parts.length === 2 ? parts[1] : null;\n        }).filter((y)=>y).map(Number).filter((y)=>!isNaN(y));\n        // Unique and descending\n        return Array.from(new Set(years)).sort((a, b)=>b - a);\n    };\n    // Filter students for selected department and passout year\n    const getFilteredStudents = ()=>{\n        let filtered = allStudents;\n        if (selectedDepartment) {\n            filtered = filtered.filter((s)=>s.department === selectedDepartment);\n        }\n        if (selectedPassoutYear) {\n            filtered = filtered.filter((s)=>{\n                if (!s.year || s.year === 'N/A') return false;\n                const parts = s.year.split('-');\n                return parts.length === 2 && parts[1] === String(selectedPassoutYear);\n            });\n        }\n        if (debouncedSearchTerm) {\n            const searchLower = debouncedSearchTerm.toLowerCase();\n            filtered = filtered.filter((student)=>student.name.toLowerCase().includes(searchLower) || student.rollNumber.toLowerCase().includes(searchLower));\n        }\n        // CGPA filter\n        filtered = filtered.filter((student)=>{\n            const cgpa = parseFloat(student.cgpa);\n            if (cgpaMin && (isNaN(cgpa) || cgpa < parseFloat(cgpaMin))) return false;\n            if (cgpaMax && (isNaN(cgpa) || cgpa > parseFloat(cgpaMax))) return false;\n            return true;\n        });\n        return filtered;\n    };\n    // Update filters and refetch when dependencies change (but not searchTerm)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentManagement.useEffect\": ()=>{\n            if (allStudents.length > 0) {\n                fetchStudents(1); // Reset to page 1 when filters change\n            }\n        }\n    }[\"StudentManagement.useEffect\"], [\n        selectedDepartment,\n        selectedYear,\n        debouncedSearchTerm,\n        selectedPassoutYear\n    ]);\n    // Filter students based on selected department, year, and search term\n    const filteredStudents = students; // Students are already filtered in fetchStudents\n    const handleStudentClick = (student)=>{\n        setSelectedStudent(student);\n        setEditedStudent({\n            ...student\n        });\n        setIsEditing(false);\n    };\n    const handleBackToList = ()=>{\n        setSelectedStudent(null);\n        setIsEditing(false);\n        setEditedStudent(null);\n    };\n    const handleBackToDepartments = ()=>{\n        setSelectedDepartment(null);\n        setSelectedYear('all');\n        setSearchTerm('');\n    };\n    const handleEdit = ()=>{\n        setIsEditing(true);\n    };\n    const handleSave = ()=>{\n        // Update the student in the list\n        setStudents((prev)=>prev.map((student)=>student.id === editedStudent.id ? editedStudent : student));\n        setSelectedStudent(editedStudent);\n        setIsEditing(false);\n    // Here you would typically make an API call to save the changes\n    };\n    const handleCancel = ()=>{\n        setEditedStudent({\n            ...selectedStudent\n        });\n        setIsEditing(false);\n    };\n    const handleInputChange = (field, value)=>{\n        setEditedStudent((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    // Handle retry button click\n    const handleRetry = ()=>{\n        setIsRetrying(true);\n        fetchStudents();\n    };\n    // Help developers find the correct API endpoint\n    const debugBackend = ()=>{\n        window.open('http://localhost:8000/admin/');\n    };\n    const handleSearch = ()=>{\n        // Force immediate search without waiting for debounce\n        setDebouncedSearchTerm(searchTerm);\n        setCurrentPage(1);\n    };\n    // Handle pagination\n    const handlePageChange = (newPage)=>{\n        if (newPage >= 1 && newPage <= totalPages) {\n            fetchStudents(newPage);\n        }\n    };\n    // Handle search input change\n    const handleSearchInputChange = (e)=>{\n        setSearchTerm(e.target.value);\n    // Don't trigger immediate search, let debounce handle it\n    };\n    // Handle search input key press\n    const handleSearchKeyDown = (e)=>{\n        if (e.key === 'Enter') {\n            e.preventDefault();\n            handleSearch();\n        }\n    };\n    if (loading) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 p-6 ml-20 overflow-y-auto h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center h-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mb-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                    lineNumber: 499,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-gray-600\",\n                    children: \"Loading students...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                    lineNumber: 500,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n            lineNumber: 498,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n        lineNumber: 497,\n        columnNumber: 5\n    }, this);\n    if (error && students.length === 0) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 p-6 ml-20 overflow-y-auto h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center h-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-red-500 mb-4 text-center max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-semibold text-lg mb-2\",\n                            children: \"Access Error\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                            lineNumber: 509,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                            lineNumber: 510,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 text-sm text-gray-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Possible solutions:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                    lineNumber: 512,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"list-disc list-inside mt-2 text-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Make sure you're logged in with admin credentials\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                            lineNumber: 514,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Check if your session has expired\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                            lineNumber: 515,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Verify Django server is running on port 8000\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                            lineNumber: 516,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Ensure proper permissions are set in Django\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                            lineNumber: 517,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                    lineNumber: 513,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                            lineNumber: 511,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                    lineNumber: 508,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-3 mt-4\",\n                    children: [\n                        !error.includes('login') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleRetry,\n                            className: \"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                            disabled: isRetrying,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_GraduationCap_RefreshCw_Save_Search_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-4 h-4 \".concat(isRetrying ? 'animate-spin' : '')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                    lineNumber: 528,\n                                    columnNumber: 15\n                                }, this),\n                                isRetrying ? 'Retrying...' : 'Retry'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                            lineNumber: 523,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>window.location.href = '/login',\n                            className: \"flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_GraduationCap_RefreshCw_Save_Search_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                    lineNumber: 536,\n                                    columnNumber: 13\n                                }, this),\n                                \"Go to Login\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                            lineNumber: 532,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                    lineNumber: 521,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n            lineNumber: 507,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n        lineNumber: 506,\n        columnNumber: 5\n    }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 p-6 ml-20 overflow-y-auto h-full\",\n        children: !selectedStudent ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: !selectedDepartment ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DepartmentCards__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                departmentOptions: departmentOptions,\n                departmentStats: departmentStats,\n                allStudents: allStudents,\n                onSelect: setSelectedDepartment\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                lineNumber: 549,\n                columnNumber: 13\n            }, this) : !selectedPassoutYear ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PassoutYearCards__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                departmentLabel: (_departmentOptions_find = departmentOptions.find((d)=>d.value === selectedDepartment)) === null || _departmentOptions_find === void 0 ? void 0 : _departmentOptions_find.label,\n                onBack: handleBackToDepartments,\n                getAvailablePassoutYears: getAvailablePassoutYears,\n                allStudents: allStudents,\n                selectedDepartment: selectedDepartment,\n                onSelectYear: setSelectedPassoutYear\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                lineNumber: 556,\n                columnNumber: 13\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StudentList__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                departmentLabel: (_departmentOptions_find1 = departmentOptions.find((d)=>d.value === selectedDepartment)) === null || _departmentOptions_find1 === void 0 ? void 0 : _departmentOptions_find1.label,\n                passoutYear: selectedPassoutYear,\n                onBack: ()=>setSelectedPassoutYear(null),\n                searchTerm: searchTerm,\n                handleSearchInputChange: handleSearchInputChange,\n                handleSearchKeyDown: handleSearchKeyDown,\n                cgpaMin: cgpaMin,\n                setCgpaMin: setCgpaMin,\n                cgpaMax: cgpaMax,\n                setCgpaMax: setCgpaMax,\n                handleSearch: handleSearch,\n                getFilteredStudents: getFilteredStudents,\n                currentPage: currentPage,\n                handlePageChange: handlePageChange,\n                handleStudentClick: handleStudentClick,\n                loading: loading\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                lineNumber: 565,\n                columnNumber: 13\n            }, this)\n        }, void 0, false) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StudentProfile__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            selectedStudent: selectedStudent,\n            editedStudent: editedStudent,\n            isEditing: isEditing,\n            handleBackToList: handleBackToList,\n            handleEdit: handleEdit,\n            handleSave: handleSave,\n            handleCancel: handleCancel,\n            handleInputChange: handleInputChange,\n            departmentOptions: departmentOptions\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n            lineNumber: 586,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n        lineNumber: 545,\n        columnNumber: 5\n    }, this);\n}\n_s(StudentManagement, \"vV3ywjzqfJaaIG37j/kyv5UkL2w=\");\n_c = StudentManagement;\nvar _c;\n$RefreshReg$(_c, \"StudentManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvYWRtaW4vc3R1ZGVudC1tYW5hZ2VtZW50L3BhZ2UuanN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVdzQjtBQUM4QjtBQUNBO0FBQ0g7QUFDRjtBQUNEO0FBQ0U7QUFDRTtBQUNWO0FBRXpCLFNBQVNrQjtRQXVoQk9DLHlCQVNBQTs7SUEvaEI3QixNQUFNLENBQUNDLFlBQVlDLGNBQWMsR0FBR1gsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDWSxxQkFBcUJDLHVCQUF1QixHQUFHYiwrQ0FBUUEsQ0FBQztJQUMvRCxNQUFNLENBQUNjLG9CQUFvQkMsc0JBQXNCLEdBQUdmLCtDQUFRQSxDQUFDO0lBQzdELE1BQU0sQ0FBQ2dCLGNBQWNDLGdCQUFnQixHQUFHakIsK0NBQVFBLENBQUM7SUFDakQsTUFBTSxDQUFDa0IsaUJBQWlCQyxtQkFBbUIsR0FBR25CLCtDQUFRQSxDQUFDO0lBQ3ZELE1BQU0sQ0FBQ29CLFdBQVdDLGFBQWEsR0FBR3JCLCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQ3NCLGVBQWVDLGlCQUFpQixHQUFHdkIsK0NBQVFBLENBQUM7SUFDbkQsTUFBTXdCLGNBQWN6Qiw2Q0FBTUEsQ0FBQztJQUMzQixNQUFNLENBQUMwQixvQkFBb0JDLHNCQUFzQixHQUFHMUIsK0NBQVFBLENBQUM7SUFDN0QsTUFBTSxDQUFDMkIsVUFBVUMsWUFBWSxHQUFHNUIsK0NBQVFBLENBQUMsRUFBRTtJQUMzQyxNQUFNLENBQUM2QixhQUFhQyxlQUFlLEdBQUc5QiwrQ0FBUUEsQ0FBQyxFQUFFO0lBQ2pELE1BQU0sQ0FBQytCLFNBQVNDLFdBQVcsR0FBR2hDLCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0sQ0FBQ2lDLE9BQU9DLFNBQVMsR0FBR2xDLCtDQUFRQSxDQUFDO0lBQ25DLE1BQU0sQ0FBQ21DLFlBQVlDLGNBQWMsR0FBR3BDLCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQ3FDLGFBQWFDLGVBQWUsR0FBR3RDLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQ3VDLFlBQVlDLGNBQWMsR0FBR3hDLCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQ3lDLGVBQWVDLGlCQUFpQixHQUFHMUMsK0NBQVFBLENBQUM7SUFDbkQsTUFBTSxDQUFDMkMsZ0JBQWdCQyxrQkFBa0IsR0FBRzVDLCtDQUFRQSxDQUFDLEVBQUU7SUFDdkQsTUFBTSxDQUFDNkMsaUJBQWlCQyxtQkFBbUIsR0FBRzlDLCtDQUFRQSxDQUFDLEVBQUU7SUFDekQsTUFBTSxDQUFDK0MscUJBQXFCQyx1QkFBdUIsR0FBR2hELCtDQUFRQSxDQUFDO0lBQy9ELE1BQU0sQ0FBQ2lELFNBQVNDLFdBQVcsR0FBR2xELCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0sQ0FBQ21ELFNBQVNDLFdBQVcsR0FBR3BELCtDQUFRQSxDQUFDO0lBRXZDLG1CQUFtQjtJQUNuQixNQUFNUyxvQkFBb0I7UUFDeEI7WUFBRTRDLE9BQU87WUFBb0JDLE9BQU87UUFBbUI7UUFDdkQ7WUFBRUQsT0FBTztZQUFlQyxPQUFPO1FBQWM7UUFDN0M7WUFBRUQsT0FBTztZQUFjQyxPQUFPO1FBQWE7UUFDM0M7WUFBRUQsT0FBTztZQUFTQyxPQUFPO1FBQVE7UUFDakM7WUFBRUQsT0FBTztZQUFjQyxPQUFPO1FBQWE7UUFDM0M7WUFBRUQsT0FBTztZQUEwQkMsT0FBTztRQUF5QjtRQUNuRTtZQUFFRCxPQUFPO1lBQVlDLE9BQU87UUFBVztRQUN2QztZQUFFRCxPQUFPO1lBQWlCQyxPQUFPO1FBQWdCO0tBQ2xEO0lBRUQsMENBQTBDO0lBQzFDLE1BQU1DLG1CQUFtQjtRQUN2QixJQUFJO1lBQ0YsTUFBTUMsUUFBUXRELHVEQUFZQTtZQUMxQixJQUFJLENBQUNzRCxPQUFPO2dCQUNWLE1BQU0sSUFBSUMsTUFBTTtZQUNsQjtZQUVBLElBQUlDLFVBQVUsRUFBRTtZQUNoQixJQUFJQyxPQUFPO1lBQ1gsSUFBSUMsVUFBVTtZQUVkLE1BQU9BLFFBQVM7Z0JBQ2QsTUFBTUMsV0FBVyxNQUFNNUQsc0RBQVdBLENBQUM2RCxXQUFXLENBQUM7b0JBQUVIO29CQUFNSSxXQUFXO2dCQUFJO2dCQUN0RUwsVUFBVTt1QkFBSUE7dUJBQVlHLFNBQVNHLElBQUk7aUJBQUM7Z0JBRXhDLElBQUlILFNBQVNJLFVBQVUsRUFBRTtvQkFDdkJMLFVBQVVELE9BQU9FLFNBQVNJLFVBQVUsQ0FBQ0MsV0FBVztvQkFDaERQO2dCQUNGLE9BQU87b0JBQ0xDLFVBQVU7Z0JBQ1o7WUFDRjtZQUVBLHlEQUF5RDtZQUN6RCxNQUFNTyxxQkFBcUIsQ0FBQ0M7Z0JBQzFCLE1BQU1DLFlBQVk7b0JBQ2hCO29CQUNBO29CQUNBO29CQUNBO29CQUNBO29CQUNBO29CQUNBO29CQUNBO2lCQUNEO2dCQUNELE9BQU9BLFNBQVMsQ0FBQ0QsUUFBUUMsVUFBVUMsTUFBTSxDQUFDO1lBQzVDO1lBRUEsK0RBQStEO1lBQy9ELE1BQU1DLDJCQUEyQixTQUFDSDtvQkFBT0ksNkVBQVk7Z0JBQ25ELElBQUlBLFdBQVc7b0JBQ2IsTUFBTUMsa0JBQWtCO3dCQUFDO3dCQUFXO3dCQUFZO3dCQUFRO3dCQUFXO3FCQUFXO29CQUM5RSxPQUFPQSxlQUFlLENBQUNMLFFBQVFLLGdCQUFnQkgsTUFBTSxDQUFDO2dCQUN4RCxPQUFPO29CQUNMLE9BQU87Z0JBQ1Q7WUFDRjtZQUVBLE1BQU1JLGVBQWVoQixRQUFRaUIsR0FBRyxDQUFDLENBQUNDLFNBQVNSLFFBQVc7b0JBQ3BEUyxJQUFJRCxRQUFRQyxFQUFFO29CQUNkQyxZQUFZRixRQUFRRyxVQUFVLElBQUk7b0JBQ2xDQyxNQUFNLEdBQStCSixPQUE1QkEsUUFBUUssVUFBVSxJQUFJLElBQUcsS0FBMkIsT0FBeEJMLFFBQVFNLFNBQVMsSUFBSSxJQUFLQyxJQUFJLE1BQU07b0JBQ3pFQyxPQUFPUixRQUFRUyxhQUFhLElBQUlULFFBQVFRLEtBQUssSUFBSTtvQkFDakRFLE9BQU9WLFFBQVFVLEtBQUssSUFBSTtvQkFDeEJDLFlBQVlYLFFBQVFZLE1BQU0sSUFBSTtvQkFDOUJDLE1BQU1DLGtCQUFrQmQsUUFBUVksTUFBTSxFQUFFWjtvQkFDeENlLE1BQU1mLFFBQVFnQixHQUFHLElBQUk7b0JBQ3JCQyxTQUFTakIsUUFBUWlCLE9BQU8sSUFBSTtvQkFDNUJDLGFBQWFsQixRQUFRbUIsYUFBYSxJQUFJO29CQUN0Q0MsZUFBZXBCLFFBQVFxQixjQUFjLElBQUk7b0JBQ3pDQyxXQUFXdEIsUUFBUXNCLFNBQVMsSUFBSTtvQkFDaENDLFFBQVF2QixRQUFRdUIsTUFBTSxJQUFJLEVBQUU7b0JBRTVCLG1CQUFtQjtvQkFDbkJDLGNBQWN4QixRQUFRd0IsWUFBWSxJQUFJeEIsUUFBUXlCLGNBQWMsSUFBSTtvQkFDaEVDLGNBQWMxQixRQUFRMEIsWUFBWSxJQUFJMUIsUUFBUTJCLGVBQWUsSUFBSTtvQkFFakUsb0JBQW9CO29CQUNwQkMsY0FBYzVCLFFBQVE0QixZQUFZLElBQUk1QixRQUFRNkIsYUFBYSxJQUFJO29CQUMvREMsb0JBQW9COUIsUUFBUThCLGtCQUFrQixJQUFJOUIsUUFBUStCLG1CQUFtQixJQUFJO29CQUNqRkMseUJBQXlCaEMsUUFBUWdDLHVCQUF1QixJQUFJaEMsUUFBUWlDLGFBQWEsSUFBSTtvQkFDckZDLGdCQUFnQmxDLFFBQVFrQyxjQUFjLElBQUlsQyxRQUFRbUMsZUFBZSxJQUFJO29CQUNyRUMsZUFBZXBDLFFBQVFvQyxhQUFhLElBQUlwQyxRQUFRcUMsY0FBYyxJQUFJO29CQUNsRUMsa0JBQWtCdEMsUUFBUXNDLGdCQUFnQixJQUFJdEMsUUFBUXVDLGlCQUFpQixJQUFJO29CQUMzRUMsd0JBQXdCeEMsUUFBUXdDLHNCQUFzQixJQUFJeEMsUUFBUXlDLGVBQWUsSUFBSTtvQkFFckYsa0JBQWtCO29CQUNsQkMsWUFBWTFDLFFBQVEwQyxVQUFVLElBQUkxQyxRQUFRMkMsYUFBYSxJQUFJO29CQUMzREMsa0JBQWtCNUMsUUFBUTRDLGdCQUFnQixJQUFJNUMsUUFBUTZDLG1CQUFtQixJQUFJO29CQUM3RUMsdUJBQXVCOUMsUUFBUThDLHFCQUFxQixJQUFJOUMsUUFBUStDLGFBQWEsSUFBSTtvQkFDakZDLGNBQWNoRCxRQUFRZ0QsWUFBWSxJQUFJaEQsUUFBUWlELGVBQWUsSUFBSTtvQkFDakVDLGFBQWFsRCxRQUFRa0QsV0FBVyxJQUFJbEQsUUFBUW1ELGNBQWMsSUFBSTtvQkFDOURDLGdCQUFnQnBELFFBQVFvRCxjQUFjLElBQUlwRCxRQUFRcUQsaUJBQWlCLElBQUk7b0JBQ3ZFQyxzQkFBc0J0RCxRQUFRc0Qsb0JBQW9CLElBQUl0RCxRQUFRdUQsZUFBZSxJQUFJO29CQUVqRixrQkFBa0I7b0JBQ2xCQyxNQUFNeEQsUUFBUXdELElBQUksSUFBSTtvQkFDdEJDLFVBQVV6RCxRQUFReUQsUUFBUSxJQUFJO29CQUM5QkMsT0FBTzFELFFBQVEwRCxLQUFLLElBQUk7b0JBQ3hCQyxTQUFTM0QsUUFBUTJELE9BQU8sSUFBSTNELFFBQVE0RCxRQUFRLElBQUk7b0JBQ2hEQyxTQUFTN0QsUUFBUTZELE9BQU8sSUFBSTtvQkFFNUIsbUJBQW1CO29CQUNuQkMsbUJBQW1COUQsUUFBUThELGlCQUFpQixJQUFJOUQsUUFBUStELG9CQUFvQixJQUFJO29CQUNoRkMscUJBQXFCaEUsUUFBUWdFLG1CQUFtQixJQUFJaEUsUUFBUWlFLG9CQUFvQixJQUFJO29CQUNwRkMsdUJBQXVCbEUsUUFBUWtFLHFCQUFxQixJQUFJbEUsUUFBUW1FLHdCQUF3QixJQUFJO29CQUM1RkMseUJBQXlCcEUsUUFBUW9FLHVCQUF1QixJQUFJcEUsUUFBUXFFLHdCQUF3QixJQUFJO29CQUVoRyxpQkFBaUI7b0JBQ2pCQyxRQUFRdEUsUUFBUXNFLE1BQU0sSUFBSTtvQkFDMUJDLFlBQVl2RSxRQUFRdUUsVUFBVSxJQUFJO29CQUVsQywwQkFBMEI7b0JBQzFCQyxnQkFBZ0J4RSxRQUFRd0UsY0FBYyxJQUFJO3dCQUN4Qzs0QkFBRUMsVUFBVTs0QkFBRzFELE1BQU07d0JBQUk7d0JBQ3pCOzRCQUFFMEQsVUFBVTs0QkFBRzFELE1BQU07d0JBQUk7d0JBQ3pCOzRCQUFFMEQsVUFBVTs0QkFBRzFELE1BQU07d0JBQUk7d0JBQ3pCOzRCQUFFMEQsVUFBVTs0QkFBRzFELE1BQU07d0JBQUk7d0JBQ3pCOzRCQUFFMEQsVUFBVTs0QkFBRzFELE1BQU07d0JBQUk7d0JBQ3pCOzRCQUFFMEQsVUFBVTs0QkFBRzFELE1BQU07d0JBQUk7d0JBQ3pCOzRCQUFFMEQsVUFBVTs0QkFBRzFELE1BQU07d0JBQUk7d0JBQ3pCOzRCQUFFMEQsVUFBVTs0QkFBRzFELE1BQU07d0JBQUk7cUJBQzFCO29CQUVELGdFQUFnRTtvQkFDaEUsR0FBSSxDQUFFZixRQUFRd0QsSUFBSSxJQUFJLENBQUN4RCxRQUFRNEIsWUFBWSxHQUFJO3dCQUM3QzRCLE1BQU07d0JBQ05DLFVBQVU7d0JBQ1ZDLE9BQU87d0JBQ1BDLFNBQVM7d0JBQ1RFLFNBQVM7d0JBQ1RyQyxjQUFjO3dCQUNkRSxjQUFjO3dCQUNkRSxjQUFjO3dCQUNkRSxvQkFBb0I7d0JBQ3BCRSx5QkFBeUI7d0JBQ3pCRSxnQkFBZ0I7d0JBQ2hCRSxlQUFlO3dCQUNmRSxrQkFBa0I7d0JBQ2xCRSx3QkFBd0I7d0JBQ3hCRSxZQUFZO3dCQUNaRSxrQkFBa0I7d0JBQ2xCRSx1QkFBdUI7d0JBQ3ZCRSxjQUFjO3dCQUNkRSxhQUFhO3dCQUNiRSxnQkFBZ0I7d0JBQ2hCRSxzQkFBc0I7b0JBQ3hCLElBQUksQ0FBQyxDQUFDO2dCQUNSO1lBRUFwRyxlQUFlNEM7WUFDZjlCLGtCQUFrQjBHLGtCQUFrQjVFO1lBQ3BDNUIsbUJBQW1CeUcsbUJBQW1CN0U7WUFFdEMsT0FBT0E7UUFDVCxFQUFFLE9BQU84RSxLQUFLO1lBQ1pDLFFBQVF4SCxLQUFLLENBQUMsZ0NBQWdDdUg7WUFDOUMsTUFBTUE7UUFDUjtJQUNGO0lBRUEsdUJBQXVCO0lBQ3ZCMUosZ0RBQVNBO3VDQUFDO1lBQ1IsTUFBTTRKLFFBQVFDO3FEQUFXO29CQUN2QjlJLHVCQUF1Qkg7Z0JBQ3pCO29EQUFHLE1BQU0sY0FBYztZQUV2QjsrQ0FBTyxJQUFNa0osYUFBYUY7O1FBQzVCO3NDQUFHO1FBQUNoSjtLQUFXO0lBRWYscURBQXFEO0lBQ3JELE1BQU1tSixnQkFBZ0I7WUFBT2xHLHdFQUFPO1FBQ2xDLElBQUk7WUFDRjNCLFdBQVc7WUFDWEUsU0FBUztZQUNURSxjQUFjO1lBRWQsTUFBTW9CLFFBQVF0RCx1REFBWUE7WUFDMUIsSUFBSSxDQUFDc0QsT0FBTztnQkFDVixNQUFNLElBQUlDLE1BQU07WUFDbEI7WUFFQSx5REFBeUQ7WUFDekQsSUFBSUMsVUFBVTdCO1lBQ2QsSUFBSUEsWUFBWXlDLE1BQU0sS0FBSyxHQUFHO2dCQUM1QlosVUFBVSxNQUFNSDtZQUNsQjtZQUVBLDRDQUE0QztZQUM1QyxJQUFJdUcsZUFBZXBHO1lBRW5CLElBQUk1QyxvQkFBb0I7Z0JBQ3RCZ0osZUFBZUEsYUFBYUMsTUFBTSxDQUFDbkYsQ0FBQUEsVUFBV0EsUUFBUVcsVUFBVSxLQUFLekU7WUFDdkU7WUFFQSxJQUFJRSxpQkFBaUIsT0FBTztnQkFDMUI4SSxlQUFlQSxhQUFhQyxNQUFNLENBQUNuRixDQUFBQSxVQUFXQSxRQUFRYSxJQUFJLEtBQUt6RTtZQUNqRTtZQUVBLElBQUlKLHFCQUFxQjtnQkFDdkIsTUFBTW9KLGNBQWNwSixvQkFBb0JxSixXQUFXO2dCQUNuREgsZUFBZUEsYUFBYUMsTUFBTSxDQUFDbkYsQ0FBQUEsVUFDakNBLFFBQVFJLElBQUksQ0FBQ2lGLFdBQVcsR0FBR0MsUUFBUSxDQUFDRixnQkFDcENwRixRQUFRRSxVQUFVLENBQUNtRixXQUFXLEdBQUdDLFFBQVEsQ0FBQ0Y7WUFFOUM7WUFFQSxtQ0FBbUM7WUFDbkMsTUFBTUcsa0JBQWtCO1lBQ3hCLE1BQU1DLGFBQWEsQ0FBQ3pHLE9BQU8sS0FBS3dHO1lBQ2hDLE1BQU1FLFdBQVdELGFBQWFEO1lBQzlCLE1BQU1HLG9CQUFvQlIsYUFBYVMsS0FBSyxDQUFDSCxZQUFZQztZQUV6RHpJLFlBQVkwSTtZQUNaaEksZUFBZXFCO1lBQ2ZuQixjQUFjZ0ksS0FBS0MsSUFBSSxDQUFDWCxhQUFheEYsTUFBTSxHQUFHNkY7WUFDOUN6SCxpQkFBaUJvSCxhQUFheEYsTUFBTTtZQUVwQ3RDLFdBQVc7UUFDYixFQUFFLE9BQU93SCxLQUFLO2dCQUdSQSxlQUVPQTtZQUpYQyxRQUFReEgsS0FBSyxDQUFDLDRCQUE0QnVIO1lBRTFDLElBQUlBLEVBQUFBLGdCQUFBQSxJQUFJM0YsUUFBUSxjQUFaMkYsb0NBQUFBLGNBQWNrQixNQUFNLE1BQUssS0FBSztnQkFDaEN4SSxTQUFTO1lBQ1gsT0FBTyxJQUFJc0gsRUFBQUEsaUJBQUFBLElBQUkzRixRQUFRLGNBQVoyRixxQ0FBQUEsZUFBY2tCLE1BQU0sTUFBSyxLQUFLO2dCQUN2Q3hJLFNBQVM7WUFDWCxPQUFPLElBQUlzSCxJQUFJbUIsT0FBTyxDQUFDVCxRQUFRLENBQUMsVUFBVTtnQkFDeENoSSxTQUFTO1lBQ1gsT0FBTztnQkFDTEEsU0FBUyxVQUFzQixPQUFac0gsSUFBSW1CLE9BQU87WUFDaEM7WUFFQS9JLFlBQVksRUFBRTtZQUNkSSxXQUFXO1FBQ2I7SUFDRjtJQUVBLCtFQUErRTtJQUMvRSxNQUFNMEQsb0JBQW9CLENBQUNGLFFBQVFaO1FBQ2pDLElBQUlBLFdBQVdBLFFBQVF3QixZQUFZLElBQUl4QixRQUFRMEIsWUFBWSxFQUFFO1lBQzNELE9BQU8sR0FBMkIxQixPQUF4QkEsUUFBUXdCLFlBQVksRUFBQyxLQUF3QixPQUFyQnhCLFFBQVEwQixZQUFZO1FBQ3hEO1FBQ0EsT0FBTztJQUNUO0lBRUEscUJBQXFCO0lBQ3JCeEcsZ0RBQVNBO3VDQUFDO1lBQ1IrSjtRQUNGO3NDQUFHLEVBQUU7SUFFTCxtREFBbUQ7SUFDbkQvSixnREFBU0E7dUNBQUM7WUFDUixpQ0FBaUM7WUFDakMsTUFBTTBELFFBQVF0RCx1REFBWUE7WUFDMUIsSUFBSSxDQUFDc0QsT0FBTztnQkFDViw4Q0FBOEM7Z0JBQzlDdEIsU0FBUztnQkFDVEYsV0FBVztnQkFDWDtZQUNGO1lBRUE2SDtRQUNGO3NDQUFHLEVBQUU7SUFFTCxtRkFBbUY7SUFDbkYsTUFBTWUsdUJBQXVCLENBQUNDO1FBQzVCLElBQUlBLGFBQWFBLFVBQVV2RyxNQUFNLElBQUksR0FBRztZQUN0QyxNQUFNd0csV0FBV0QsVUFBVUUsU0FBUyxDQUFDLEdBQUc7WUFDeEMsSUFBSSxDQUFDQyxNQUFNRixXQUFXO2dCQUNwQixPQUFPLEdBQXVELE9BQXBELElBQUssS0FBSUcsT0FBT0MsV0FBVyxLQUFLQyxTQUFTTCxTQUFRLEdBQUc7WUFDaEU7UUFDRjtRQUNBLE9BQU87SUFDVDtJQUVBLHVDQUF1QztJQUN2Q2hMLGdEQUFTQTt1Q0FBQztZQUNSLE1BQU1zTDtrRUFBcUIsQ0FBQ0M7b0JBQzFCLElBQUk3SixZQUFZOEosT0FBTyxJQUFJLENBQUM5SixZQUFZOEosT0FBTyxDQUFDQyxRQUFRLENBQUNGLE1BQU1HLE1BQU0sR0FBRzt3QkFDdEU5SixzQkFBc0I7b0JBQ3hCO2dCQUNGOztZQUVBK0osU0FBU0MsZ0JBQWdCLENBQUMsYUFBYU47WUFDdkM7K0NBQU87b0JBQ0xLLFNBQVNFLG1CQUFtQixDQUFDLGFBQWFQO2dCQUM1Qzs7UUFDRjtzQ0FBRyxFQUFFO0lBRUwseUNBQXlDO0lBQ3pDLE1BQU05QixvQkFBb0IsQ0FBQzVFO1FBQ3pCLE1BQU1rSCxRQUFRO2VBQUksSUFBSUMsSUFBSW5ILGFBQWFDLEdBQUcsQ0FBQ0MsQ0FBQUEsVUFBV0EsUUFBUWEsSUFBSSxFQUFFc0UsTUFBTSxDQUFDdEUsQ0FBQUEsT0FBUUEsUUFBUUEsU0FBUztTQUFRO1FBQzVHLE9BQU9tRyxNQUFNRSxJQUFJO0lBQ25CO0lBRUEsNEJBQTRCO0lBQzVCLE1BQU12QyxxQkFBcUIsQ0FBQzdFO1FBQzFCLE1BQU1xSCxRQUFRLENBQUM7UUFDZnJILGFBQWFzSCxPQUFPLENBQUNwSCxDQUFBQTtZQUNuQixJQUFJQSxRQUFRVyxVQUFVLElBQUlYLFFBQVFXLFVBQVUsS0FBSyxPQUFPO2dCQUN0RHdHLEtBQUssQ0FBQ25ILFFBQVFXLFVBQVUsQ0FBQyxHQUFHLENBQUN3RyxLQUFLLENBQUNuSCxRQUFRVyxVQUFVLENBQUMsSUFBSSxLQUFLO1lBQ2pFO1FBQ0Y7UUFDQSxPQUFPMEcsT0FBT0MsT0FBTyxDQUFDSCxPQUFPcEgsR0FBRyxDQUFDO2dCQUFDLENBQUNZLFlBQVk0RyxNQUFNO21CQUFNO2dCQUFFNUc7Z0JBQVk0RztZQUFNOztJQUNqRjtJQUVBLHNEQUFzRDtJQUN0RCxNQUFNQywyQkFBMkI7UUFDL0IsSUFBSSxDQUFDdEwsb0JBQW9CLE9BQU8sRUFBRTtRQUNsQyxNQUFNOEssUUFBUS9KLFlBQ1hrSSxNQUFNLENBQUNzQyxDQUFBQSxJQUFLQSxFQUFFOUcsVUFBVSxLQUFLekUsc0JBQXNCdUwsRUFBRTVHLElBQUksSUFBSTRHLEVBQUU1RyxJQUFJLEtBQUssT0FDeEVkLEdBQUcsQ0FBQzBILENBQUFBO1lBQ0gsb0VBQW9FO1lBQ3BFLE1BQU1DLFFBQVFELEVBQUU1RyxJQUFJLENBQUM4RyxLQUFLLENBQUM7WUFDM0IsT0FBT0QsTUFBTWhJLE1BQU0sS0FBSyxJQUFJZ0ksS0FBSyxDQUFDLEVBQUUsR0FBRztRQUN6QyxHQUNDdkMsTUFBTSxDQUFDeUMsQ0FBQUEsSUFBS0EsR0FDWjdILEdBQUcsQ0FBQzhILFFBQ0oxQyxNQUFNLENBQUN5QyxDQUFBQSxJQUFLLENBQUN4QixNQUFNd0I7UUFDdEIsd0JBQXdCO1FBQ3hCLE9BQU9FLE1BQU1DLElBQUksQ0FBQyxJQUFJZCxJQUFJRCxRQUFRRSxJQUFJLENBQUMsQ0FBQ2MsR0FBR0MsSUFBTUEsSUFBSUQ7SUFDdkQ7SUFFQSwyREFBMkQ7SUFDM0QsTUFBTUUsc0JBQXNCO1FBQzFCLElBQUlDLFdBQVdsTDtRQUNmLElBQUlmLG9CQUFvQjtZQUN0QmlNLFdBQVdBLFNBQVNoRCxNQUFNLENBQUNzQyxDQUFBQSxJQUFLQSxFQUFFOUcsVUFBVSxLQUFLekU7UUFDbkQ7UUFDQSxJQUFJaUMscUJBQXFCO1lBQ3ZCZ0ssV0FBV0EsU0FBU2hELE1BQU0sQ0FBQ3NDLENBQUFBO2dCQUN6QixJQUFJLENBQUNBLEVBQUU1RyxJQUFJLElBQUk0RyxFQUFFNUcsSUFBSSxLQUFLLE9BQU8sT0FBTztnQkFDeEMsTUFBTTZHLFFBQVFELEVBQUU1RyxJQUFJLENBQUM4RyxLQUFLLENBQUM7Z0JBQzNCLE9BQU9ELE1BQU1oSSxNQUFNLEtBQUssS0FBS2dJLEtBQUssQ0FBQyxFQUFFLEtBQUtVLE9BQU9qSztZQUNuRDtRQUNGO1FBQ0EsSUFBSW5DLHFCQUFxQjtZQUN2QixNQUFNb0osY0FBY3BKLG9CQUFvQnFKLFdBQVc7WUFDbkQ4QyxXQUFXQSxTQUFTaEQsTUFBTSxDQUFDbkYsQ0FBQUEsVUFDekJBLFFBQVFJLElBQUksQ0FBQ2lGLFdBQVcsR0FBR0MsUUFBUSxDQUFDRixnQkFDcENwRixRQUFRRSxVQUFVLENBQUNtRixXQUFXLEdBQUdDLFFBQVEsQ0FBQ0Y7UUFFOUM7UUFDQSxjQUFjO1FBQ2QrQyxXQUFXQSxTQUFTaEQsTUFBTSxDQUFDbkYsQ0FBQUE7WUFDekIsTUFBTWUsT0FBT3NILFdBQVdySSxRQUFRZSxJQUFJO1lBQ3BDLElBQUkxQyxXQUFZK0gsQ0FBQUEsTUFBTXJGLFNBQVNBLE9BQU9zSCxXQUFXaEssUUFBTyxHQUFJLE9BQU87WUFDbkUsSUFBSUUsV0FBWTZILENBQUFBLE1BQU1yRixTQUFTQSxPQUFPc0gsV0FBVzlKLFFBQU8sR0FBSSxPQUFPO1lBQ25FLE9BQU87UUFDVDtRQUNBLE9BQU80SjtJQUNUO0lBRUEsMkVBQTJFO0lBQzNFak4sZ0RBQVNBO3VDQUFDO1lBQ1IsSUFBSStCLFlBQVl5QyxNQUFNLEdBQUcsR0FBRztnQkFDMUJ1RixjQUFjLElBQUksc0NBQXNDO1lBQzFEO1FBQ0Y7c0NBQUc7UUFBQy9JO1FBQW9CRTtRQUFjSjtRQUFxQm1DO0tBQW9CO0lBRS9FLHNFQUFzRTtJQUN0RSxNQUFNbUssbUJBQW1CdkwsVUFBVSxpREFBaUQ7SUFFcEYsTUFBTXdMLHFCQUFxQixDQUFDdkk7UUFDMUJ6RCxtQkFBbUJ5RDtRQUNuQnJELGlCQUFpQjtZQUFFLEdBQUdxRCxPQUFPO1FBQUM7UUFDOUJ2RCxhQUFhO0lBQ2Y7SUFFQSxNQUFNK0wsbUJBQW1CO1FBQ3ZCak0sbUJBQW1CO1FBQ25CRSxhQUFhO1FBQ2JFLGlCQUFpQjtJQUNuQjtJQUVBLE1BQU04TCwwQkFBMEI7UUFDOUJ0TSxzQkFBc0I7UUFDdEJFLGdCQUFnQjtRQUNoQk4sY0FBYztJQUNoQjtJQUVBLE1BQU0yTSxhQUFhO1FBQ2pCak0sYUFBYTtJQUNmO0lBRUEsTUFBTWtNLGFBQWE7UUFDakIsaUNBQWlDO1FBQ2pDM0wsWUFBWTRMLENBQUFBLE9BQ1ZBLEtBQUs3SSxHQUFHLENBQUNDLENBQUFBLFVBQ1BBLFFBQVFDLEVBQUUsS0FBS3ZELGNBQWN1RCxFQUFFLEdBQUd2RCxnQkFBZ0JzRDtRQUd0RHpELG1CQUFtQkc7UUFDbkJELGFBQWE7SUFDYixnRUFBZ0U7SUFDbEU7SUFFQSxNQUFNb00sZUFBZTtRQUNuQmxNLGlCQUFpQjtZQUFFLEdBQUdMLGVBQWU7UUFBQztRQUN0Q0csYUFBYTtJQUNmO0lBRUEsTUFBTXFNLG9CQUFvQixDQUFDQyxPQUFPdEs7UUFDaEM5QixpQkFBaUJpTSxDQUFBQSxPQUFTO2dCQUN4QixHQUFHQSxJQUFJO2dCQUNQLENBQUNHLE1BQU0sRUFBRXRLO1lBQ1g7SUFDRjtJQUVBLDRCQUE0QjtJQUM1QixNQUFNdUssY0FBYztRQUNsQnhMLGNBQWM7UUFDZHlIO0lBQ0Y7SUFFQSxnREFBZ0Q7SUFDaEQsTUFBTWdFLGVBQWU7UUFDbkJDLE9BQU9DLElBQUksQ0FBQztJQUNkO0lBRUEsTUFBTUMsZUFBZTtRQUNuQixzREFBc0Q7UUFDdERuTix1QkFBdUJIO1FBQ3ZCNEIsZUFBZTtJQUNqQjtJQUVBLG9CQUFvQjtJQUNwQixNQUFNMkwsbUJBQW1CLENBQUNDO1FBQ3hCLElBQUlBLFdBQVcsS0FBS0EsV0FBVzNMLFlBQVk7WUFDekNzSCxjQUFjcUU7UUFDaEI7SUFDRjtJQUVBLDZCQUE2QjtJQUM3QixNQUFNQywwQkFBMEIsQ0FBQ0M7UUFDL0J6TixjQUFjeU4sRUFBRTVDLE1BQU0sQ0FBQ25JLEtBQUs7SUFDNUIseURBQXlEO0lBQzNEO0lBRUEsZ0NBQWdDO0lBQ2hDLE1BQU1nTCxzQkFBc0IsQ0FBQ0Q7UUFDM0IsSUFBSUEsRUFBRUUsR0FBRyxLQUFLLFNBQVM7WUFDckJGLEVBQUVHLGNBQWM7WUFDaEJQO1FBQ0Y7SUFDRjtJQUVBLElBQUlqTSxTQUFTLHFCQUNYLDhEQUFDeU07UUFBSUMsV0FBVTtrQkFDYiw0RUFBQ0Q7WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUNEO29CQUFJQyxXQUFVOzs7Ozs7OEJBQ2YsOERBQUNEO29CQUFJQyxXQUFVOzhCQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFLckMsSUFBSXhNLFNBQVNOLFNBQVMyQyxNQUFNLEtBQUssR0FBRyxxQkFDbEMsOERBQUNrSztRQUFJQyxXQUFVO2tCQUNiLDRFQUFDRDtZQUFJQyxXQUFVOzs4QkFDYiw4REFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDQzs0QkFBRUQsV0FBVTtzQ0FBNkI7Ozs7OztzQ0FDMUMsOERBQUNDO3NDQUFHek07Ozs7OztzQ0FDSiw4REFBQ3VNOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0M7OENBQUU7Ozs7Ozs4Q0FDSCw4REFBQ0M7b0NBQUdGLFdBQVU7O3NEQUNaLDhEQUFDRztzREFBRzs7Ozs7O3NEQUNKLDhEQUFDQTtzREFBRzs7Ozs7O3NEQUNKLDhEQUFDQTtzREFBRzs7Ozs7O3NEQUNKLDhEQUFDQTtzREFBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQUlWLDhEQUFDSjtvQkFBSUMsV0FBVTs7d0JBQ1osQ0FBQ3hNLE1BQU1pSSxRQUFRLENBQUMsMEJBQ2YsOERBQUMyRTs0QkFDQ0MsU0FBU2xCOzRCQUNUYSxXQUFVOzRCQUNWTSxVQUFVNU07OzhDQUVWLDhEQUFDM0MseUlBQVNBO29DQUFDaVAsV0FBVyxXQUE0QyxPQUFqQ3RNLGFBQWEsaUJBQWlCOzs7Ozs7Z0NBQzlEQSxhQUFhLGdCQUFnQjs7Ozs7OztzQ0FHbEMsOERBQUMwTTs0QkFDQ0MsU0FBUyxJQUFNaEIsT0FBT2tCLFFBQVEsQ0FBQ0MsSUFBSSxHQUFHOzRCQUN0Q1IsV0FBVTs7OENBRVYsOERBQUM5TywwSUFBSUE7b0NBQUM4TyxXQUFVOzs7Ozs7Z0NBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQVF0QyxxQkFDRSw4REFBQ0Q7UUFBSUMsV0FBVTtrQkFDWixDQUFDdk4sZ0NBQ0E7c0JBQ0csQ0FBQ0osbUNBQ0EsOERBQUNULHdEQUFlQTtnQkFDZEksbUJBQW1CQTtnQkFDbkJvQyxpQkFBaUJBO2dCQUNqQmhCLGFBQWFBO2dCQUNicU4sVUFBVW5POzs7Ozt1QkFFVixDQUFDZ0Msb0NBQ0gsOERBQUN6Qyx5REFBZ0JBO2dCQUNmNk8sZUFBZSxHQUFFMU8sMEJBQUFBLGtCQUFrQjJPLElBQUksQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRWhNLEtBQUssS0FBS3ZDLGlDQUF4Q0wsOENBQUFBLHdCQUE2RDZDLEtBQUs7Z0JBQ25GZ00sUUFBUWpDO2dCQUNSakIsMEJBQTBCQTtnQkFDMUJ2SyxhQUFhQTtnQkFDYmYsb0JBQW9CQTtnQkFDcEJ5TyxjQUFjdk07Ozs7O3FDQUdoQiw4REFBQ3pDLG9EQUFXQTtnQkFDVjRPLGVBQWUsR0FBRTFPLDJCQUFBQSxrQkFBa0IyTyxJQUFJLENBQUNDLENBQUFBLElBQUtBLEVBQUVoTSxLQUFLLEtBQUt2QyxpQ0FBeENMLCtDQUFBQSx5QkFBNkQ2QyxLQUFLO2dCQUNuRmtNLGFBQWF6TTtnQkFDYnVNLFFBQVEsSUFBTXRNLHVCQUF1QjtnQkFDckN0QyxZQUFZQTtnQkFDWnlOLHlCQUF5QkE7Z0JBQ3pCRSxxQkFBcUJBO2dCQUNyQnBMLFNBQVNBO2dCQUNUQyxZQUFZQTtnQkFDWkMsU0FBU0E7Z0JBQ1RDLFlBQVlBO2dCQUNaNEssY0FBY0E7Z0JBQ2RsQixxQkFBcUJBO2dCQUNyQnpLLGFBQWFBO2dCQUNiNEwsa0JBQWtCQTtnQkFDbEJkLG9CQUFvQkE7Z0JBQ3BCcEwsU0FBU0E7Ozs7OzswQ0FLZiw4REFBQzNCLHVEQUFjQTtZQUNiYyxpQkFBaUJBO1lBQ2pCSSxlQUFlQTtZQUNmRixXQUFXQTtZQUNYZ00sa0JBQWtCQTtZQUNsQkUsWUFBWUE7WUFDWkMsWUFBWUE7WUFDWkUsY0FBY0E7WUFDZEMsbUJBQW1CQTtZQUNuQmpOLG1CQUFtQkE7Ozs7Ozs7Ozs7O0FBSzdCO0dBbGtCd0JEO0tBQUFBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHBnYXV0XFxEb2N1bWVudHNcXFZTIENPREVcXGNvbWJpbmVcXGZyb250ZW5kXFxzcmNcXGFwcFxcYWRtaW5cXHN0dWRlbnQtbWFuYWdlbWVudFxccGFnZS5qc3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQge1xuICBBcnJvd0xlZnQsXG4gIENhbGVuZGFyLFxuICBSZWZyZXNoQ3csXG4gIFNhdmUsXG4gIFNlYXJjaCxcbiAgVXNlcixcbiAgWCxcbiAgR3JhZHVhdGlvbkNhcFxufSBmcm9tIFwibHVjaWRlLXJlYWN0XCI7XG5pbXBvcnQgeyB1c2VFZmZlY3QsIHVzZVJlZiwgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBzdHVkZW50c0FQSSB9IGZyb20gJy4uLy4uLy4uL2FwaS9zdHVkZW50cyc7XG5pbXBvcnQgeyBnZXRBdXRoVG9rZW4gfSBmcm9tICcuLi8uLi8uLi9hcGkvYXV0aCc7XG5pbXBvcnQgQ3VzdG9tRHJvcGRvd24gZnJvbSAnLi9TdHVkZW50RHJvcGRvd24nO1xuaW1wb3J0IFN0dWRlbnRQcm9maWxlIGZyb20gJy4vU3R1ZGVudFByb2ZpbGUnO1xuaW1wb3J0IERlcGFydG1lbnRDYXJkcyBmcm9tICcuL0RlcGFydG1lbnRDYXJkcyc7XG5pbXBvcnQgUGFzc291dFllYXJDYXJkcyBmcm9tICcuL1Bhc3NvdXRZZWFyQ2FyZHMnO1xuaW1wb3J0IFN0dWRlbnRMaXN0IGZyb20gJy4vU3R1ZGVudExpc3QnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBTdHVkZW50TWFuYWdlbWVudCgpIHtcbiAgY29uc3QgW3NlYXJjaFRlcm0sIHNldFNlYXJjaFRlcm1dID0gdXNlU3RhdGUoJycpO1xuICBjb25zdCBbZGVib3VuY2VkU2VhcmNoVGVybSwgc2V0RGVib3VuY2VkU2VhcmNoVGVybV0gPSB1c2VTdGF0ZSgnJyk7XG4gIGNvbnN0IFtzZWxlY3RlZERlcGFydG1lbnQsIHNldFNlbGVjdGVkRGVwYXJ0bWVudF0gPSB1c2VTdGF0ZShudWxsKTtcbiAgY29uc3QgW3NlbGVjdGVkWWVhciwgc2V0U2VsZWN0ZWRZZWFyXSA9IHVzZVN0YXRlKCdhbGwnKTtcbiAgY29uc3QgW3NlbGVjdGVkU3R1ZGVudCwgc2V0U2VsZWN0ZWRTdHVkZW50XSA9IHVzZVN0YXRlKG51bGwpO1xuICBjb25zdCBbaXNFZGl0aW5nLCBzZXRJc0VkaXRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbZWRpdGVkU3R1ZGVudCwgc2V0RWRpdGVkU3R1ZGVudF0gPSB1c2VTdGF0ZShudWxsKTtcbiAgY29uc3QgZHJvcGRvd25SZWYgPSB1c2VSZWYobnVsbCk7XG4gIGNvbnN0IFtpc1llYXJEcm9wZG93bk9wZW4sIHNldElzWWVhckRyb3Bkb3duT3Blbl0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtzdHVkZW50cywgc2V0U3R1ZGVudHNdID0gdXNlU3RhdGUoW10pO1xuICBjb25zdCBbYWxsU3R1ZGVudHMsIHNldEFsbFN0dWRlbnRzXSA9IHVzZVN0YXRlKFtdKTtcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSk7XG4gIGNvbnN0IFtlcnJvciwgc2V0RXJyb3JdID0gdXNlU3RhdGUobnVsbCk7XG4gIGNvbnN0IFtpc1JldHJ5aW5nLCBzZXRJc1JldHJ5aW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2N1cnJlbnRQYWdlLCBzZXRDdXJyZW50UGFnZV0gPSB1c2VTdGF0ZSgxKTtcbiAgY29uc3QgW3RvdGFsUGFnZXMsIHNldFRvdGFsUGFnZXNdID0gdXNlU3RhdGUoMSk7XG4gIGNvbnN0IFt0b3RhbFN0dWRlbnRzLCBzZXRUb3RhbFN0dWRlbnRzXSA9IHVzZVN0YXRlKDApO1xuICBjb25zdCBbYXZhaWxhYmxlWWVhcnMsIHNldEF2YWlsYWJsZVllYXJzXSA9IHVzZVN0YXRlKFtdKTtcbiAgY29uc3QgW2RlcGFydG1lbnRTdGF0cywgc2V0RGVwYXJ0bWVudFN0YXRzXSA9IHVzZVN0YXRlKFtdKTtcbiAgY29uc3QgW3NlbGVjdGVkUGFzc291dFllYXIsIHNldFNlbGVjdGVkUGFzc291dFllYXJdID0gdXNlU3RhdGUobnVsbCk7XG4gIGNvbnN0IFtjZ3BhTWluLCBzZXRDZ3BhTWluXSA9IHVzZVN0YXRlKCcnKTtcbiAgY29uc3QgW2NncGFNYXgsIHNldENncGFNYXhdID0gdXNlU3RhdGUoJycpO1xuXG4gIC8vIERyb3Bkb3duIG9wdGlvbnNcbiAgY29uc3QgZGVwYXJ0bWVudE9wdGlvbnMgPSBbXG4gICAgeyB2YWx1ZTogJ0NvbXB1dGVyIFNjaWVuY2UnLCBsYWJlbDogJ0NvbXB1dGVyIFNjaWVuY2UnIH0sXG4gICAgeyB2YWx1ZTogJ0VsZWN0cm9uaWNzJywgbGFiZWw6ICdFbGVjdHJvbmljcycgfSxcbiAgICB7IHZhbHVlOiAnTWVjaGFuaWNhbCcsIGxhYmVsOiAnTWVjaGFuaWNhbCcgfSxcbiAgICB7IHZhbHVlOiAnQ2l2aWwnLCBsYWJlbDogJ0NpdmlsJyB9LFxuICAgIHsgdmFsdWU6ICdFbGVjdHJpY2FsJywgbGFiZWw6ICdFbGVjdHJpY2FsJyB9LFxuICAgIHsgdmFsdWU6ICdJbmZvcm1hdGlvbiBUZWNobm9sb2d5JywgbGFiZWw6ICdJbmZvcm1hdGlvbiBUZWNobm9sb2d5JyB9LFxuICAgIHsgdmFsdWU6ICdDaGVtaWNhbCcsIGxhYmVsOiAnQ2hlbWljYWwnIH0sXG4gICAgeyB2YWx1ZTogJ0Jpb3RlY2hub2xvZ3knLCBsYWJlbDogJ0Jpb3RlY2hub2xvZ3knIH1cbiAgXTtcblxuICAvLyBGZXRjaCBhbGwgc3R1ZGVudHMgZm9yIGNvbXBsZXRlIGRhdGFzZXRcbiAgY29uc3QgZmV0Y2hBbGxTdHVkZW50cyA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgdG9rZW4gPSBnZXRBdXRoVG9rZW4oKTtcbiAgICAgIGlmICghdG9rZW4pIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdObyBhdXRoZW50aWNhdGlvbiB0b2tlbiBmb3VuZC4gUGxlYXNlIGxvZ2luIGZpcnN0LicpO1xuICAgICAgfVxuXG4gICAgICBsZXQgYWxsRGF0YSA9IFtdO1xuICAgICAgbGV0IHBhZ2UgPSAxO1xuICAgICAgbGV0IGhhc01vcmUgPSB0cnVlO1xuXG4gICAgICB3aGlsZSAoaGFzTW9yZSkge1xuICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHN0dWRlbnRzQVBJLmdldFN0dWRlbnRzKHsgcGFnZSwgcGFnZV9zaXplOiAxMDAgfSk7XG4gICAgICAgIGFsbERhdGEgPSBbLi4uYWxsRGF0YSwgLi4ucmVzcG9uc2UuZGF0YV07XG4gICAgICAgIFxuICAgICAgICBpZiAocmVzcG9uc2UucGFnaW5hdGlvbikge1xuICAgICAgICAgIGhhc01vcmUgPSBwYWdlIDwgcmVzcG9uc2UucGFnaW5hdGlvbi50b3RhbF9wYWdlcztcbiAgICAgICAgICBwYWdlKys7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgaGFzTW9yZSA9IGZhbHNlO1xuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIC8vIEhlbHBlciBmdW5jdGlvbiB0byBnZXQgZGVmYXVsdCBsb2NhdGlvbiBiYXNlZCBvbiBpbmRleFxuICAgICAgY29uc3QgZ2V0RGVmYXVsdExvY2F0aW9uID0gKGluZGV4KSA9PiB7XG4gICAgICAgIGNvbnN0IGxvY2F0aW9ucyA9IFtcbiAgICAgICAgICAnTXVtYmFpLCBNYWhhcmFzaHRyYScsXG4gICAgICAgICAgJ0RlbGhpLCBEZWxoaScsXG4gICAgICAgICAgJ0JhbmdhbG9yZSwgS2FybmF0YWthJyxcbiAgICAgICAgICAnQ2hlbm5haSwgVGFtaWwgTmFkdScsXG4gICAgICAgICAgJ1B1bmUsIE1haGFyYXNodHJhJyxcbiAgICAgICAgICAnSHlkZXJhYmFkLCBUZWxhbmdhbmEnLFxuICAgICAgICAgICdLb2xrYXRhLCBXZXN0IEJlbmdhbCcsXG4gICAgICAgICAgJ0FobWVkYWJhZCwgR3VqYXJhdCdcbiAgICAgICAgXTtcbiAgICAgICAgcmV0dXJuIGxvY2F0aW9uc1tpbmRleCAlIGxvY2F0aW9ucy5sZW5ndGhdO1xuICAgICAgfTtcblxuICAgICAgLy8gSGVscGVyIGZ1bmN0aW9uIHRvIGdldCBkZWZhdWx0IHNwZWNpYWxpemF0aW9uIGJhc2VkIG9uIGluZGV4XG4gICAgICBjb25zdCBnZXREZWZhdWx0U3BlY2lhbGl6YXRpb24gPSAoaW5kZXgsIGlzQ2xhc3MxMiA9IHRydWUpID0+IHtcbiAgICAgICAgaWYgKGlzQ2xhc3MxMikge1xuICAgICAgICAgIGNvbnN0IHNwZWNpYWxpemF0aW9ucyA9IFsnU2NpZW5jZScsICdDb21tZXJjZScsICdBcnRzJywgJ1NjaWVuY2UnLCAnQ29tbWVyY2UnXTtcbiAgICAgICAgICByZXR1cm4gc3BlY2lhbGl6YXRpb25zW2luZGV4ICUgc3BlY2lhbGl6YXRpb25zLmxlbmd0aF07XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgcmV0dXJuICdHZW5lcmFsJztcbiAgICAgICAgfVxuICAgICAgfTtcblxuICAgICAgY29uc3Qgc3R1ZGVudHNEYXRhID0gYWxsRGF0YS5tYXAoKHN0dWRlbnQsIGluZGV4KSA9PiAoe1xuICAgICAgICBpZDogc3R1ZGVudC5pZCxcbiAgICAgICAgcm9sbE51bWJlcjogc3R1ZGVudC5zdHVkZW50X2lkIHx8ICdOL0EnLFxuICAgICAgICBuYW1lOiBgJHtzdHVkZW50LmZpcnN0X25hbWUgfHwgJyd9ICR7c3R1ZGVudC5sYXN0X25hbWUgfHwgJyd9YC50cmltKCkgfHwgJ1Vua25vd24nLFxuICAgICAgICBlbWFpbDogc3R1ZGVudC5jb250YWN0X2VtYWlsIHx8IHN0dWRlbnQuZW1haWwgfHwgJ04vQScsXG4gICAgICAgIHBob25lOiBzdHVkZW50LnBob25lIHx8ICdOL0EnLFxuICAgICAgICBkZXBhcnRtZW50OiBzdHVkZW50LmJyYW5jaCB8fCAnTi9BJyxcbiAgICAgICAgeWVhcjogZ2V0WWVhckZyb21CcmFuY2goc3R1ZGVudC5icmFuY2gsIHN0dWRlbnQpLFxuICAgICAgICBjZ3BhOiBzdHVkZW50LmdwYSB8fCAnTi9BJyxcbiAgICAgICAgYWRkcmVzczogc3R1ZGVudC5hZGRyZXNzIHx8ICdOL0EnLFxuICAgICAgICBkYXRlT2ZCaXJ0aDogc3R1ZGVudC5kYXRlX29mX2JpcnRoIHx8ICcnLFxuICAgICAgICBwYXJlbnRDb250YWN0OiBzdHVkZW50LnBhcmVudF9jb250YWN0IHx8ICdOL0EnLFxuICAgICAgICBlZHVjYXRpb246IHN0dWRlbnQuZWR1Y2F0aW9uIHx8ICdOL0EnLFxuICAgICAgICBza2lsbHM6IHN0dWRlbnQuc2tpbGxzIHx8IFtdLFxuXG4gICAgICAgIC8vIEFjYWRlbWljIGRldGFpbHNcbiAgICAgICAgam9pbmluZ195ZWFyOiBzdHVkZW50LmpvaW5pbmdfeWVhciB8fCBzdHVkZW50LmFkbWlzc2lvbl95ZWFyIHx8ICcnLFxuICAgICAgICBwYXNzb3V0X3llYXI6IHN0dWRlbnQucGFzc291dF95ZWFyIHx8IHN0dWRlbnQuZ3JhZHVhdGlvbl95ZWFyIHx8ICcnLFxuXG4gICAgICAgIC8vIENsYXNzIFhJSSBkZXRhaWxzXG4gICAgICAgIHR3ZWxmdGhfY2dwYTogc3R1ZGVudC50d2VsZnRoX2NncGEgfHwgc3R1ZGVudC5jbGFzc18xMl9jZ3BhIHx8ICcnLFxuICAgICAgICB0d2VsZnRoX3BlcmNlbnRhZ2U6IHN0dWRlbnQudHdlbGZ0aF9wZXJjZW50YWdlIHx8IHN0dWRlbnQuY2xhc3NfMTJfcGVyY2VudGFnZSB8fCAnJyxcbiAgICAgICAgdHdlbGZ0aF95ZWFyX29mX3Bhc3Npbmc6IHN0dWRlbnQudHdlbGZ0aF95ZWFyX29mX3Bhc3NpbmcgfHwgc3R1ZGVudC5jbGFzc18xMl95ZWFyIHx8ICcnLFxuICAgICAgICB0d2VsZnRoX3NjaG9vbDogc3R1ZGVudC50d2VsZnRoX3NjaG9vbCB8fCBzdHVkZW50LmNsYXNzXzEyX3NjaG9vbCB8fCAnJyxcbiAgICAgICAgdHdlbGZ0aF9ib2FyZDogc3R1ZGVudC50d2VsZnRoX2JvYXJkIHx8IHN0dWRlbnQuY2xhc3NfMTJfYm9hcmQgfHwgJycsXG4gICAgICAgIHR3ZWxmdGhfbG9jYXRpb246IHN0dWRlbnQudHdlbGZ0aF9sb2NhdGlvbiB8fCBzdHVkZW50LmNsYXNzXzEyX2xvY2F0aW9uIHx8ICdNdW1iYWksIE1haGFyYXNodHJhJyxcbiAgICAgICAgdHdlbGZ0aF9zcGVjaWFsaXphdGlvbjogc3R1ZGVudC50d2VsZnRoX3NwZWNpYWxpemF0aW9uIHx8IHN0dWRlbnQuY2xhc3NfMTJfc3RyZWFtIHx8ICdTY2llbmNlJyxcblxuICAgICAgICAvLyBDbGFzcyBYIGRldGFpbHNcbiAgICAgICAgdGVudGhfY2dwYTogc3R1ZGVudC50ZW50aF9jZ3BhIHx8IHN0dWRlbnQuY2xhc3NfMTBfY2dwYSB8fCAnJyxcbiAgICAgICAgdGVudGhfcGVyY2VudGFnZTogc3R1ZGVudC50ZW50aF9wZXJjZW50YWdlIHx8IHN0dWRlbnQuY2xhc3NfMTBfcGVyY2VudGFnZSB8fCAnJyxcbiAgICAgICAgdGVudGhfeWVhcl9vZl9wYXNzaW5nOiBzdHVkZW50LnRlbnRoX3llYXJfb2ZfcGFzc2luZyB8fCBzdHVkZW50LmNsYXNzXzEwX3llYXIgfHwgJycsXG4gICAgICAgIHRlbnRoX3NjaG9vbDogc3R1ZGVudC50ZW50aF9zY2hvb2wgfHwgc3R1ZGVudC5jbGFzc18xMF9zY2hvb2wgfHwgJycsXG4gICAgICAgIHRlbnRoX2JvYXJkOiBzdHVkZW50LnRlbnRoX2JvYXJkIHx8IHN0dWRlbnQuY2xhc3NfMTBfYm9hcmQgfHwgJycsXG4gICAgICAgIHRlbnRoX2xvY2F0aW9uOiBzdHVkZW50LnRlbnRoX2xvY2F0aW9uIHx8IHN0dWRlbnQuY2xhc3NfMTBfbG9jYXRpb24gfHwgJ011bWJhaSwgTWFoYXJhc2h0cmEnLFxuICAgICAgICB0ZW50aF9zcGVjaWFsaXphdGlvbjogc3R1ZGVudC50ZW50aF9zcGVjaWFsaXphdGlvbiB8fCBzdHVkZW50LmNsYXNzXzEwX3N0cmVhbSB8fCAnR2VuZXJhbCcsXG5cbiAgICAgICAgLy8gQWRkcmVzcyBkZXRhaWxzXG4gICAgICAgIGNpdHk6IHN0dWRlbnQuY2l0eSB8fCAnJyxcbiAgICAgICAgZGlzdHJpY3Q6IHN0dWRlbnQuZGlzdHJpY3QgfHwgJycsXG4gICAgICAgIHN0YXRlOiBzdHVkZW50LnN0YXRlIHx8ICcnLFxuICAgICAgICBwaW5jb2RlOiBzdHVkZW50LnBpbmNvZGUgfHwgc3R1ZGVudC5waW5fY29kZSB8fCAnJyxcbiAgICAgICAgY291bnRyeTogc3R1ZGVudC5jb3VudHJ5IHx8ICdJbmRpYScsXG5cbiAgICAgICAgLy8gQ2VydGlmaWNhdGUgVVJMc1xuICAgICAgICB0ZW50aF9jZXJ0aWZpY2F0ZTogc3R1ZGVudC50ZW50aF9jZXJ0aWZpY2F0ZSB8fCBzdHVkZW50LmNsYXNzXzEwX2NlcnRpZmljYXRlIHx8ICcnLFxuICAgICAgICB0d2VsZnRoX2NlcnRpZmljYXRlOiBzdHVkZW50LnR3ZWxmdGhfY2VydGlmaWNhdGUgfHwgc3R1ZGVudC5jbGFzc18xMl9jZXJ0aWZpY2F0ZSB8fCAnJyxcbiAgICAgICAgdGVudGhfY2VydGlmaWNhdGVfdXJsOiBzdHVkZW50LnRlbnRoX2NlcnRpZmljYXRlX3VybCB8fCBzdHVkZW50LmNsYXNzXzEwX2NlcnRpZmljYXRlX3VybCB8fCAnJyxcbiAgICAgICAgdHdlbGZ0aF9jZXJ0aWZpY2F0ZV91cmw6IHN0dWRlbnQudHdlbGZ0aF9jZXJ0aWZpY2F0ZV91cmwgfHwgc3R1ZGVudC5jbGFzc18xMl9jZXJ0aWZpY2F0ZV91cmwgfHwgJycsXG5cbiAgICAgICAgLy8gUmVzdW1lIGRldGFpbHNcbiAgICAgICAgcmVzdW1lOiBzdHVkZW50LnJlc3VtZSB8fCAnJyxcbiAgICAgICAgcmVzdW1lX3VybDogc3R1ZGVudC5yZXN1bWVfdXJsIHx8ICcnLFxuXG4gICAgICAgIC8vIFNlbWVzdGVyLXdpc2UgQ0dQQSBkYXRhXG4gICAgICAgIHNlbWVzdGVyX2NncGFzOiBzdHVkZW50LnNlbWVzdGVyX2NncGFzIHx8IFtcbiAgICAgICAgICB7IHNlbWVzdGVyOiAxLCBjZ3BhOiA4LjIgfSxcbiAgICAgICAgICB7IHNlbWVzdGVyOiAyLCBjZ3BhOiA4LjUgfSxcbiAgICAgICAgICB7IHNlbWVzdGVyOiAzLCBjZ3BhOiA4LjcgfSxcbiAgICAgICAgICB7IHNlbWVzdGVyOiA0LCBjZ3BhOiA4LjQgfSxcbiAgICAgICAgICB7IHNlbWVzdGVyOiA1LCBjZ3BhOiA4LjYgfSxcbiAgICAgICAgICB7IHNlbWVzdGVyOiA2LCBjZ3BhOiA4LjggfSxcbiAgICAgICAgICB7IHNlbWVzdGVyOiA3LCBjZ3BhOiA4LjMgfSxcbiAgICAgICAgICB7IHNlbWVzdGVyOiA4LCBjZ3BhOiA4LjUgfVxuICAgICAgICBdLFxuXG4gICAgICAgIC8vIEFkZCBzb21lIG1vY2sgZGF0YSBpZiBmaWVsZHMgYXJlIGVtcHR5IChmb3IgdGVzdGluZyBwdXJwb3NlcylcbiAgICAgICAgLi4uKCghc3R1ZGVudC5jaXR5ICYmICFzdHVkZW50LnR3ZWxmdGhfY2dwYSkgPyB7XG4gICAgICAgICAgY2l0eTogJ011bWJhaScsXG4gICAgICAgICAgZGlzdHJpY3Q6ICdNdW1iYWknLFxuICAgICAgICAgIHN0YXRlOiAnTWFoYXJhc2h0cmEnLFxuICAgICAgICAgIHBpbmNvZGU6ICc0MDAwMDEnLFxuICAgICAgICAgIGNvdW50cnk6ICdJbmRpYScsXG4gICAgICAgICAgam9pbmluZ195ZWFyOiAnMjAyMCcsXG4gICAgICAgICAgcGFzc291dF95ZWFyOiAnMjAyNCcsXG4gICAgICAgICAgdHdlbGZ0aF9jZ3BhOiAnOS4yJyxcbiAgICAgICAgICB0d2VsZnRoX3BlcmNlbnRhZ2U6ICc4Ny40JScsXG4gICAgICAgICAgdHdlbGZ0aF95ZWFyX29mX3Bhc3Npbmc6ICcyMDIwJyxcbiAgICAgICAgICB0d2VsZnRoX3NjaG9vbDogJ0FCQyBKdW5pb3IgQ29sbGVnZScsXG4gICAgICAgICAgdHdlbGZ0aF9ib2FyZDogJ01haGFyYXNodHJhIFN0YXRlIEJvYXJkJyxcbiAgICAgICAgICB0d2VsZnRoX2xvY2F0aW9uOiAnTXVtYmFpLCBNYWhhcmFzaHRyYScsXG4gICAgICAgICAgdHdlbGZ0aF9zcGVjaWFsaXphdGlvbjogJ1NjaWVuY2UnLFxuICAgICAgICAgIHRlbnRoX2NncGE6ICc5LjAnLFxuICAgICAgICAgIHRlbnRoX3BlcmNlbnRhZ2U6ICc4NS42JScsXG4gICAgICAgICAgdGVudGhfeWVhcl9vZl9wYXNzaW5nOiAnMjAxOCcsXG4gICAgICAgICAgdGVudGhfc2Nob29sOiAnWFlaIEhpZ2ggU2Nob29sJyxcbiAgICAgICAgICB0ZW50aF9ib2FyZDogJ01haGFyYXNodHJhIFN0YXRlIEJvYXJkJyxcbiAgICAgICAgICB0ZW50aF9sb2NhdGlvbjogJ011bWJhaSwgTWFoYXJhc2h0cmEnLFxuICAgICAgICAgIHRlbnRoX3NwZWNpYWxpemF0aW9uOiAnR2VuZXJhbCdcbiAgICAgICAgfSA6IHt9KVxuICAgICAgfSkpO1xuXG4gICAgICBzZXRBbGxTdHVkZW50cyhzdHVkZW50c0RhdGEpO1xuICAgICAgc2V0QXZhaWxhYmxlWWVhcnMoZ2V0QXZhaWxhYmxlWWVhcnMoc3R1ZGVudHNEYXRhKSk7XG4gICAgICBzZXREZXBhcnRtZW50U3RhdHMoZ2V0RGVwYXJ0bWVudFN0YXRzKHN0dWRlbnRzRGF0YSkpO1xuICAgICAgXG4gICAgICByZXR1cm4gc3R1ZGVudHNEYXRhO1xuICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgYWxsIHN0dWRlbnRzOicsIGVycik7XG4gICAgICB0aHJvdyBlcnI7XG4gICAgfVxuICB9O1xuXG4gIC8vIERlYm91bmNlIHNlYXJjaCB0ZXJtXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgdGltZXIgPSBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgIHNldERlYm91bmNlZFNlYXJjaFRlcm0oc2VhcmNoVGVybSk7XG4gICAgfSwgMzAwKTsgLy8gMzAwbXMgZGVsYXlcblxuICAgIHJldHVybiAoKSA9PiBjbGVhclRpbWVvdXQodGltZXIpO1xuICB9LCBbc2VhcmNoVGVybV0pO1xuXG4gIC8vIEZldGNoIHN0dWRlbnRzIGZyb20gRGphbmdvIGJhY2tlbmQgd2l0aCBwYWdpbmF0aW9uXG4gIGNvbnN0IGZldGNoU3R1ZGVudHMgPSBhc3luYyAocGFnZSA9IDEpID0+IHtcbiAgICB0cnkge1xuICAgICAgc2V0TG9hZGluZyh0cnVlKTtcbiAgICAgIHNldEVycm9yKG51bGwpO1xuICAgICAgc2V0SXNSZXRyeWluZyhmYWxzZSk7XG4gICAgICBcbiAgICAgIGNvbnN0IHRva2VuID0gZ2V0QXV0aFRva2VuKCk7XG4gICAgICBpZiAoIXRva2VuKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignTm8gYXV0aGVudGljYXRpb24gdG9rZW4gZm91bmQuIFBsZWFzZSBsb2dpbiBmaXJzdC4nKTtcbiAgICAgIH1cblxuICAgICAgLy8gVXNlIGFsbFN0dWRlbnRzIGlmIGFscmVhZHkgbG9hZGVkLCBvdGhlcndpc2UgZmV0Y2ggYWxsXG4gICAgICBsZXQgYWxsRGF0YSA9IGFsbFN0dWRlbnRzO1xuICAgICAgaWYgKGFsbFN0dWRlbnRzLmxlbmd0aCA9PT0gMCkge1xuICAgICAgICBhbGxEYXRhID0gYXdhaXQgZmV0Y2hBbGxTdHVkZW50cygpO1xuICAgICAgfVxuICAgICAgXG4gICAgICAvLyBBcHBseSBmaWx0ZXJzIHRvIGdldCB0aGUgZmlsdGVyZWQgZGF0YXNldFxuICAgICAgbGV0IGZpbHRlcmVkRGF0YSA9IGFsbERhdGE7XG4gICAgICBcbiAgICAgIGlmIChzZWxlY3RlZERlcGFydG1lbnQpIHtcbiAgICAgICAgZmlsdGVyZWREYXRhID0gZmlsdGVyZWREYXRhLmZpbHRlcihzdHVkZW50ID0+IHN0dWRlbnQuZGVwYXJ0bWVudCA9PT0gc2VsZWN0ZWREZXBhcnRtZW50KTtcbiAgICAgIH1cbiAgICAgIFxuICAgICAgaWYgKHNlbGVjdGVkWWVhciAhPT0gJ2FsbCcpIHtcbiAgICAgICAgZmlsdGVyZWREYXRhID0gZmlsdGVyZWREYXRhLmZpbHRlcihzdHVkZW50ID0+IHN0dWRlbnQueWVhciA9PT0gc2VsZWN0ZWRZZWFyKTtcbiAgICAgIH1cbiAgICAgIFxuICAgICAgaWYgKGRlYm91bmNlZFNlYXJjaFRlcm0pIHtcbiAgICAgICAgY29uc3Qgc2VhcmNoTG93ZXIgPSBkZWJvdW5jZWRTZWFyY2hUZXJtLnRvTG93ZXJDYXNlKCk7XG4gICAgICAgIGZpbHRlcmVkRGF0YSA9IGZpbHRlcmVkRGF0YS5maWx0ZXIoc3R1ZGVudCA9PiBcbiAgICAgICAgICBzdHVkZW50Lm5hbWUudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhzZWFyY2hMb3dlcikgfHxcbiAgICAgICAgICBzdHVkZW50LnJvbGxOdW1iZXIudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhzZWFyY2hMb3dlcilcbiAgICAgICAgKTtcbiAgICAgIH1cblxuICAgICAgLy8gSW1wbGVtZW50IGNsaWVudC1zaWRlIHBhZ2luYXRpb25cbiAgICAgIGNvbnN0IHN0dWRlbnRzUGVyUGFnZSA9IDEwO1xuICAgICAgY29uc3Qgc3RhcnRJbmRleCA9IChwYWdlIC0gMSkgKiBzdHVkZW50c1BlclBhZ2U7XG4gICAgICBjb25zdCBlbmRJbmRleCA9IHN0YXJ0SW5kZXggKyBzdHVkZW50c1BlclBhZ2U7XG4gICAgICBjb25zdCBwYWdpbmF0ZWRTdHVkZW50cyA9IGZpbHRlcmVkRGF0YS5zbGljZShzdGFydEluZGV4LCBlbmRJbmRleCk7XG4gICAgICBcbiAgICAgIHNldFN0dWRlbnRzKHBhZ2luYXRlZFN0dWRlbnRzKTtcbiAgICAgIHNldEN1cnJlbnRQYWdlKHBhZ2UpO1xuICAgICAgc2V0VG90YWxQYWdlcyhNYXRoLmNlaWwoZmlsdGVyZWREYXRhLmxlbmd0aCAvIHN0dWRlbnRzUGVyUGFnZSkpO1xuICAgICAgc2V0VG90YWxTdHVkZW50cyhmaWx0ZXJlZERhdGEubGVuZ3RoKTtcbiAgICAgIFxuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyBzdHVkZW50czonLCBlcnIpO1xuICAgICAgXG4gICAgICBpZiAoZXJyLnJlc3BvbnNlPy5zdGF0dXMgPT09IDQwMSkge1xuICAgICAgICBzZXRFcnJvcignQXV0aGVudGljYXRpb24gZmFpbGVkLiBQbGVhc2UgbG9naW4gYWdhaW4uJyk7XG4gICAgICB9IGVsc2UgaWYgKGVyci5yZXNwb25zZT8uc3RhdHVzID09PSA0MDMpIHtcbiAgICAgICAgc2V0RXJyb3IoJ1lvdSBkbyBub3QgaGF2ZSBwZXJtaXNzaW9uIHRvIHZpZXcgc3R1ZGVudHMuIEFkbWluIGFjY2VzcyByZXF1aXJlZC4nKTtcbiAgICAgIH0gZWxzZSBpZiAoZXJyLm1lc3NhZ2UuaW5jbHVkZXMoJ3Rva2VuJykpIHtcbiAgICAgICAgc2V0RXJyb3IoJ1BsZWFzZSBsb2dpbiB0byBhY2Nlc3Mgc3R1ZGVudCBtYW5hZ2VtZW50LicpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgc2V0RXJyb3IoYEVycm9yOiAke2Vyci5tZXNzYWdlfWApO1xuICAgICAgfVxuICAgICAgXG4gICAgICBzZXRTdHVkZW50cyhbXSk7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgLy8gSGVscGVyIGZ1bmN0aW9uIHRvIGRldGVybWluZSB5ZWFyIGZyb20gYnJhbmNoICh5b3UgY2FuIGN1c3RvbWl6ZSB0aGlzIGxvZ2ljKVxuICBjb25zdCBnZXRZZWFyRnJvbUJyYW5jaCA9IChicmFuY2gsIHN0dWRlbnQpID0+IHtcbiAgICBpZiAoc3R1ZGVudCAmJiBzdHVkZW50LmpvaW5pbmdfeWVhciAmJiBzdHVkZW50LnBhc3NvdXRfeWVhcikge1xuICAgICAgcmV0dXJuIGAke3N0dWRlbnQuam9pbmluZ195ZWFyfS0ke3N0dWRlbnQucGFzc291dF95ZWFyfWA7XG4gICAgfVxuICAgIHJldHVybiAnTi9BJztcbiAgfTtcblxuICAvLyBJbml0aWFsIGRhdGEgZmV0Y2hcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBmZXRjaFN0dWRlbnRzKCk7XG4gIH0sIFtdKTtcblxuICAvLyBBZGQgdGhpcyB1c2VFZmZlY3QgYWZ0ZXIgeW91ciBleGlzdGluZyB1c2VFZmZlY3RcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAvLyBDaGVjayBpZiB1c2VyIGlzIGF1dGhlbnRpY2F0ZWRcbiAgICBjb25zdCB0b2tlbiA9IGdldEF1dGhUb2tlbigpO1xuICAgIGlmICghdG9rZW4pIHtcbiAgICAgIC8vIFJlZGlyZWN0IHRvIGxvZ2luIHBhZ2Ugb3Igc2hvdyBsb2dpbiBwcm9tcHRcbiAgICAgIHNldEVycm9yKCdQbGVhc2UgbG9naW4gdG8gYWNjZXNzIHN0dWRlbnQgbWFuYWdlbWVudC4nKTtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICBcbiAgICBmZXRjaFN0dWRlbnRzKCk7XG4gIH0sIFtdKTtcblxuICAvLyBIZWxwZXIgZnVuY3Rpb24gdG8gZXh0cmFjdCB5ZWFyIGZyb20gc3R1ZGVudCBJRCAoYXNzdW1pbmcgZm9ybWF0IGxpa2UgQ1MyMDIxMDAxKVxuICBjb25zdCBnZXRZZWFyRnJvbVN0dWRlbnRJZCA9IChzdHVkZW50SWQpID0+IHtcbiAgICBpZiAoc3R1ZGVudElkICYmIHN0dWRlbnRJZC5sZW5ndGggPj0gNikge1xuICAgICAgY29uc3QgeWVhclBhcnQgPSBzdHVkZW50SWQuc3Vic3RyaW5nKDIsIDYpO1xuICAgICAgaWYgKCFpc05hTih5ZWFyUGFydCkpIHtcbiAgICAgICAgcmV0dXJuIGAkezQgLSAobmV3IERhdGUoKS5nZXRGdWxsWWVhcigpIC0gcGFyc2VJbnQoeWVhclBhcnQpKX10aCBZZWFyYDtcbiAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuICdVbmtub3duJztcbiAgfTtcblxuICAvLyBDbG9zZSBkcm9wZG93biB3aGVuIGNsaWNraW5nIG91dHNpZGVcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBoYW5kbGVDbGlja091dHNpZGUgPSAoZXZlbnQpID0+IHtcbiAgICAgIGlmIChkcm9wZG93blJlZi5jdXJyZW50ICYmICFkcm9wZG93blJlZi5jdXJyZW50LmNvbnRhaW5zKGV2ZW50LnRhcmdldCkpIHtcbiAgICAgICAgc2V0SXNZZWFyRHJvcGRvd25PcGVuKGZhbHNlKTtcbiAgICAgIH1cbiAgICB9O1xuXG4gICAgZG9jdW1lbnQuYWRkRXZlbnRMaXN0ZW5lcignbW91c2Vkb3duJywgaGFuZGxlQ2xpY2tPdXRzaWRlKTtcbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgZG9jdW1lbnQucmVtb3ZlRXZlbnRMaXN0ZW5lcignbW91c2Vkb3duJywgaGFuZGxlQ2xpY2tPdXRzaWRlKTtcbiAgICB9O1xuICB9LCBbXSk7XG5cbiAgLy8gR2V0IGF2YWlsYWJsZSB5ZWFycyBmcm9tIHN0dWRlbnRzIGRhdGFcbiAgY29uc3QgZ2V0QXZhaWxhYmxlWWVhcnMgPSAoc3R1ZGVudHNEYXRhKSA9PiB7XG4gICAgY29uc3QgeWVhcnMgPSBbLi4ubmV3IFNldChzdHVkZW50c0RhdGEubWFwKHN0dWRlbnQgPT4gc3R1ZGVudC55ZWFyKS5maWx0ZXIoeWVhciA9PiB5ZWFyICYmIHllYXIgIT09ICdOL0EnKSldO1xuICAgIHJldHVybiB5ZWFycy5zb3J0KCk7XG4gIH07XG5cbiAgLy8gR2V0IGRlcGFydG1lbnQgc3RhdGlzdGljc1xuICBjb25zdCBnZXREZXBhcnRtZW50U3RhdHMgPSAoc3R1ZGVudHNEYXRhKSA9PiB7XG4gICAgY29uc3Qgc3RhdHMgPSB7fTtcbiAgICBzdHVkZW50c0RhdGEuZm9yRWFjaChzdHVkZW50ID0+IHtcbiAgICAgIGlmIChzdHVkZW50LmRlcGFydG1lbnQgJiYgc3R1ZGVudC5kZXBhcnRtZW50ICE9PSAnTi9BJykge1xuICAgICAgICBzdGF0c1tzdHVkZW50LmRlcGFydG1lbnRdID0gKHN0YXRzW3N0dWRlbnQuZGVwYXJ0bWVudF0gfHwgMCkgKyAxO1xuICAgICAgfVxuICAgIH0pO1xuICAgIHJldHVybiBPYmplY3QuZW50cmllcyhzdGF0cykubWFwKChbZGVwYXJ0bWVudCwgY291bnRdKSA9PiAoeyBkZXBhcnRtZW50LCBjb3VudCB9KSk7XG4gIH07XG5cbiAgLy8gR2V0IGF2YWlsYWJsZSBwYXNzb3V0IHllYXJzIGZvciBzZWxlY3RlZCBkZXBhcnRtZW50XG4gIGNvbnN0IGdldEF2YWlsYWJsZVBhc3NvdXRZZWFycyA9ICgpID0+IHtcbiAgICBpZiAoIXNlbGVjdGVkRGVwYXJ0bWVudCkgcmV0dXJuIFtdO1xuICAgIGNvbnN0IHllYXJzID0gYWxsU3R1ZGVudHNcbiAgICAgIC5maWx0ZXIocyA9PiBzLmRlcGFydG1lbnQgPT09IHNlbGVjdGVkRGVwYXJ0bWVudCAmJiBzLnllYXIgJiYgcy55ZWFyICE9PSAnTi9BJylcbiAgICAgIC5tYXAocyA9PiB7XG4gICAgICAgIC8vIEV4dHJhY3QgcGFzc291dCB5ZWFyIGZyb20geWVhciBzdHJpbmcgKGZvcm1hdDogXCJqb2luaW5nLXBhc3NvdXRcIilcbiAgICAgICAgY29uc3QgcGFydHMgPSBzLnllYXIuc3BsaXQoJy0nKTtcbiAgICAgICAgcmV0dXJuIHBhcnRzLmxlbmd0aCA9PT0gMiA/IHBhcnRzWzFdIDogbnVsbDtcbiAgICAgIH0pXG4gICAgICAuZmlsdGVyKHkgPT4geSlcbiAgICAgIC5tYXAoTnVtYmVyKVxuICAgICAgLmZpbHRlcih5ID0+ICFpc05hTih5KSk7XG4gICAgLy8gVW5pcXVlIGFuZCBkZXNjZW5kaW5nXG4gICAgcmV0dXJuIEFycmF5LmZyb20obmV3IFNldCh5ZWFycykpLnNvcnQoKGEsIGIpID0+IGIgLSBhKTtcbiAgfTtcblxuICAvLyBGaWx0ZXIgc3R1ZGVudHMgZm9yIHNlbGVjdGVkIGRlcGFydG1lbnQgYW5kIHBhc3NvdXQgeWVhclxuICBjb25zdCBnZXRGaWx0ZXJlZFN0dWRlbnRzID0gKCkgPT4ge1xuICAgIGxldCBmaWx0ZXJlZCA9IGFsbFN0dWRlbnRzO1xuICAgIGlmIChzZWxlY3RlZERlcGFydG1lbnQpIHtcbiAgICAgIGZpbHRlcmVkID0gZmlsdGVyZWQuZmlsdGVyKHMgPT4gcy5kZXBhcnRtZW50ID09PSBzZWxlY3RlZERlcGFydG1lbnQpO1xuICAgIH1cbiAgICBpZiAoc2VsZWN0ZWRQYXNzb3V0WWVhcikge1xuICAgICAgZmlsdGVyZWQgPSBmaWx0ZXJlZC5maWx0ZXIocyA9PiB7XG4gICAgICAgIGlmICghcy55ZWFyIHx8IHMueWVhciA9PT0gJ04vQScpIHJldHVybiBmYWxzZTtcbiAgICAgICAgY29uc3QgcGFydHMgPSBzLnllYXIuc3BsaXQoJy0nKTtcbiAgICAgICAgcmV0dXJuIHBhcnRzLmxlbmd0aCA9PT0gMiAmJiBwYXJ0c1sxXSA9PT0gU3RyaW5nKHNlbGVjdGVkUGFzc291dFllYXIpO1xuICAgICAgfSk7XG4gICAgfVxuICAgIGlmIChkZWJvdW5jZWRTZWFyY2hUZXJtKSB7XG4gICAgICBjb25zdCBzZWFyY2hMb3dlciA9IGRlYm91bmNlZFNlYXJjaFRlcm0udG9Mb3dlckNhc2UoKTtcbiAgICAgIGZpbHRlcmVkID0gZmlsdGVyZWQuZmlsdGVyKHN0dWRlbnQgPT4gXG4gICAgICAgIHN0dWRlbnQubmFtZS50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaExvd2VyKSB8fFxuICAgICAgICBzdHVkZW50LnJvbGxOdW1iZXIudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhzZWFyY2hMb3dlcilcbiAgICAgICk7XG4gICAgfVxuICAgIC8vIENHUEEgZmlsdGVyXG4gICAgZmlsdGVyZWQgPSBmaWx0ZXJlZC5maWx0ZXIoc3R1ZGVudCA9PiB7XG4gICAgICBjb25zdCBjZ3BhID0gcGFyc2VGbG9hdChzdHVkZW50LmNncGEpO1xuICAgICAgaWYgKGNncGFNaW4gJiYgKGlzTmFOKGNncGEpIHx8IGNncGEgPCBwYXJzZUZsb2F0KGNncGFNaW4pKSkgcmV0dXJuIGZhbHNlO1xuICAgICAgaWYgKGNncGFNYXggJiYgKGlzTmFOKGNncGEpIHx8IGNncGEgPiBwYXJzZUZsb2F0KGNncGFNYXgpKSkgcmV0dXJuIGZhbHNlO1xuICAgICAgcmV0dXJuIHRydWU7XG4gICAgfSk7XG4gICAgcmV0dXJuIGZpbHRlcmVkO1xuICB9O1xuXG4gIC8vIFVwZGF0ZSBmaWx0ZXJzIGFuZCByZWZldGNoIHdoZW4gZGVwZW5kZW5jaWVzIGNoYW5nZSAoYnV0IG5vdCBzZWFyY2hUZXJtKVxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChhbGxTdHVkZW50cy5sZW5ndGggPiAwKSB7XG4gICAgICBmZXRjaFN0dWRlbnRzKDEpOyAvLyBSZXNldCB0byBwYWdlIDEgd2hlbiBmaWx0ZXJzIGNoYW5nZVxuICAgIH1cbiAgfSwgW3NlbGVjdGVkRGVwYXJ0bWVudCwgc2VsZWN0ZWRZZWFyLCBkZWJvdW5jZWRTZWFyY2hUZXJtLCBzZWxlY3RlZFBhc3NvdXRZZWFyXSk7XG5cbiAgLy8gRmlsdGVyIHN0dWRlbnRzIGJhc2VkIG9uIHNlbGVjdGVkIGRlcGFydG1lbnQsIHllYXIsIGFuZCBzZWFyY2ggdGVybVxuICBjb25zdCBmaWx0ZXJlZFN0dWRlbnRzID0gc3R1ZGVudHM7IC8vIFN0dWRlbnRzIGFyZSBhbHJlYWR5IGZpbHRlcmVkIGluIGZldGNoU3R1ZGVudHNcblxuICBjb25zdCBoYW5kbGVTdHVkZW50Q2xpY2sgPSAoc3R1ZGVudCkgPT4ge1xuICAgIHNldFNlbGVjdGVkU3R1ZGVudChzdHVkZW50KTtcbiAgICBzZXRFZGl0ZWRTdHVkZW50KHsgLi4uc3R1ZGVudCB9KTtcbiAgICBzZXRJc0VkaXRpbmcoZmFsc2UpO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUJhY2tUb0xpc3QgPSAoKSA9PiB7XG4gICAgc2V0U2VsZWN0ZWRTdHVkZW50KG51bGwpO1xuICAgIHNldElzRWRpdGluZyhmYWxzZSk7XG4gICAgc2V0RWRpdGVkU3R1ZGVudChudWxsKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVCYWNrVG9EZXBhcnRtZW50cyA9ICgpID0+IHtcbiAgICBzZXRTZWxlY3RlZERlcGFydG1lbnQobnVsbCk7XG4gICAgc2V0U2VsZWN0ZWRZZWFyKCdhbGwnKTtcbiAgICBzZXRTZWFyY2hUZXJtKCcnKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVFZGl0ID0gKCkgPT4ge1xuICAgIHNldElzRWRpdGluZyh0cnVlKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVTYXZlID0gKCkgPT4ge1xuICAgIC8vIFVwZGF0ZSB0aGUgc3R1ZGVudCBpbiB0aGUgbGlzdFxuICAgIHNldFN0dWRlbnRzKHByZXYgPT4gXG4gICAgICBwcmV2Lm1hcChzdHVkZW50ID0+IFxuICAgICAgICBzdHVkZW50LmlkID09PSBlZGl0ZWRTdHVkZW50LmlkID8gZWRpdGVkU3R1ZGVudCA6IHN0dWRlbnRcbiAgICAgIClcbiAgICApO1xuICAgIHNldFNlbGVjdGVkU3R1ZGVudChlZGl0ZWRTdHVkZW50KTtcbiAgICBzZXRJc0VkaXRpbmcoZmFsc2UpO1xuICAgIC8vIEhlcmUgeW91IHdvdWxkIHR5cGljYWxseSBtYWtlIGFuIEFQSSBjYWxsIHRvIHNhdmUgdGhlIGNoYW5nZXNcbiAgfTtcblxuICBjb25zdCBoYW5kbGVDYW5jZWwgPSAoKSA9PiB7XG4gICAgc2V0RWRpdGVkU3R1ZGVudCh7IC4uLnNlbGVjdGVkU3R1ZGVudCB9KTtcbiAgICBzZXRJc0VkaXRpbmcoZmFsc2UpO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUlucHV0Q2hhbmdlID0gKGZpZWxkLCB2YWx1ZSkgPT4ge1xuICAgIHNldEVkaXRlZFN0dWRlbnQocHJldiA9PiAoe1xuICAgICAgLi4ucHJldixcbiAgICAgIFtmaWVsZF06IHZhbHVlXG4gICAgfSkpO1xuICB9O1xuXG4gIC8vIEhhbmRsZSByZXRyeSBidXR0b24gY2xpY2tcbiAgY29uc3QgaGFuZGxlUmV0cnkgPSAoKSA9PiB7XG4gICAgc2V0SXNSZXRyeWluZyh0cnVlKTtcbiAgICBmZXRjaFN0dWRlbnRzKCk7XG4gIH07XG5cbiAgLy8gSGVscCBkZXZlbG9wZXJzIGZpbmQgdGhlIGNvcnJlY3QgQVBJIGVuZHBvaW50XG4gIGNvbnN0IGRlYnVnQmFja2VuZCA9ICgpID0+IHtcbiAgICB3aW5kb3cub3BlbignaHR0cDovL2xvY2FsaG9zdDo4MDAwL2FkbWluLycpO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZVNlYXJjaCA9ICgpID0+IHtcbiAgICAvLyBGb3JjZSBpbW1lZGlhdGUgc2VhcmNoIHdpdGhvdXQgd2FpdGluZyBmb3IgZGVib3VuY2VcbiAgICBzZXREZWJvdW5jZWRTZWFyY2hUZXJtKHNlYXJjaFRlcm0pO1xuICAgIHNldEN1cnJlbnRQYWdlKDEpO1xuICB9O1xuXG4gIC8vIEhhbmRsZSBwYWdpbmF0aW9uXG4gIGNvbnN0IGhhbmRsZVBhZ2VDaGFuZ2UgPSAobmV3UGFnZSkgPT4ge1xuICAgIGlmIChuZXdQYWdlID49IDEgJiYgbmV3UGFnZSA8PSB0b3RhbFBhZ2VzKSB7XG4gICAgICBmZXRjaFN0dWRlbnRzKG5ld1BhZ2UpO1xuICAgIH1cbiAgfTtcblxuICAvLyBIYW5kbGUgc2VhcmNoIGlucHV0IGNoYW5nZVxuICBjb25zdCBoYW5kbGVTZWFyY2hJbnB1dENoYW5nZSA9IChlKSA9PiB7XG4gICAgc2V0U2VhcmNoVGVybShlLnRhcmdldC52YWx1ZSk7XG4gICAgLy8gRG9uJ3QgdHJpZ2dlciBpbW1lZGlhdGUgc2VhcmNoLCBsZXQgZGVib3VuY2UgaGFuZGxlIGl0XG4gIH07XG5cbiAgLy8gSGFuZGxlIHNlYXJjaCBpbnB1dCBrZXkgcHJlc3NcbiAgY29uc3QgaGFuZGxlU2VhcmNoS2V5RG93biA9IChlKSA9PiB7XG4gICAgaWYgKGUua2V5ID09PSAnRW50ZXInKSB7XG4gICAgICBlLnByZXZlbnREZWZhdWx0KCk7XG4gICAgICBoYW5kbGVTZWFyY2goKTtcbiAgICB9XG4gIH07XG5cbiAgaWYgKGxvYWRpbmcpIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgcC02IG1sLTIwIG92ZXJmbG93LXktYXV0byBoLWZ1bGxcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgaC1mdWxsXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTEyIHctMTIgYm9yZGVyLXQtMiBib3JkZXItYi0yIGJvcmRlci1ibHVlLTUwMCBtYi00XCI+PC9kaXY+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPkxvYWRpbmcgc3R1ZGVudHMuLi48L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xuICBcbiAgaWYgKGVycm9yICYmIHN0dWRlbnRzLmxlbmd0aCA9PT0gMCkgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBwLTYgbWwtMjAgb3ZlcmZsb3cteS1hdXRvIGgtZnVsbFwiPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBoLWZ1bGxcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXJlZC01MDAgbWItNCB0ZXh0LWNlbnRlciBtYXgtdy1tZFwiPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgdGV4dC1sZyBtYi0yXCI+QWNjZXNzIEVycm9yPC9wPlxuICAgICAgICAgIDxwPntlcnJvcn08L3A+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC00IHRleHQtc20gdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgPHA+UG9zc2libGUgc29sdXRpb25zOjwvcD5cbiAgICAgICAgICAgIDx1bCBjbGFzc05hbWU9XCJsaXN0LWRpc2MgbGlzdC1pbnNpZGUgbXQtMiB0ZXh0LWxlZnRcIj5cbiAgICAgICAgICAgICAgPGxpPk1ha2Ugc3VyZSB5b3UncmUgbG9nZ2VkIGluIHdpdGggYWRtaW4gY3JlZGVudGlhbHM8L2xpPlxuICAgICAgICAgICAgICA8bGk+Q2hlY2sgaWYgeW91ciBzZXNzaW9uIGhhcyBleHBpcmVkPC9saT5cbiAgICAgICAgICAgICAgPGxpPlZlcmlmeSBEamFuZ28gc2VydmVyIGlzIHJ1bm5pbmcgb24gcG9ydCA4MDAwPC9saT5cbiAgICAgICAgICAgICAgPGxpPkVuc3VyZSBwcm9wZXIgcGVybWlzc2lvbnMgYXJlIHNldCBpbiBEamFuZ288L2xpPlxuICAgICAgICAgICAgPC91bD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtMyBtdC00XCI+XG4gICAgICAgICAgeyFlcnJvci5pbmNsdWRlcygnbG9naW4nKSAmJiAoXG4gICAgICAgICAgICA8YnV0dG9uIFxuICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVSZXRyeX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgcHgtNCBweS0yIGJnLWJsdWUtNjAwIHRleHQtd2hpdGUgcm91bmRlZC1sZyBob3ZlcjpiZy1ibHVlLTcwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgIGRpc2FibGVkPXtpc1JldHJ5aW5nfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8UmVmcmVzaEN3IGNsYXNzTmFtZT17YHctNCBoLTQgJHtpc1JldHJ5aW5nID8gJ2FuaW1hdGUtc3BpbicgOiAnJ31gfSAvPlxuICAgICAgICAgICAgICB7aXNSZXRyeWluZyA/ICdSZXRyeWluZy4uLicgOiAnUmV0cnknfVxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgKX1cbiAgICAgICAgICA8YnV0dG9uIFxuICAgICAgICAgICAgb25DbGljaz17KCkgPT4gd2luZG93LmxvY2F0aW9uLmhyZWYgPSAnL2xvZ2luJ31cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yIHB4LTQgcHktMiBiZy1ncmVlbi02MDAgdGV4dC13aGl0ZSByb3VuZGVkLWxnIGhvdmVyOmJnLWdyZWVuLTcwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPFVzZXIgY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XG4gICAgICAgICAgICBHbyB0byBMb2dpblxuICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgcC02IG1sLTIwIG92ZXJmbG93LXktYXV0byBoLWZ1bGxcIj5cbiAgICAgIHshc2VsZWN0ZWRTdHVkZW50ID8gKFxuICAgICAgICA8PlxuICAgICAgICAgIHshc2VsZWN0ZWREZXBhcnRtZW50ID8gKFxuICAgICAgICAgICAgPERlcGFydG1lbnRDYXJkc1xuICAgICAgICAgICAgICBkZXBhcnRtZW50T3B0aW9ucz17ZGVwYXJ0bWVudE9wdGlvbnN9XG4gICAgICAgICAgICAgIGRlcGFydG1lbnRTdGF0cz17ZGVwYXJ0bWVudFN0YXRzfVxuICAgICAgICAgICAgICBhbGxTdHVkZW50cz17YWxsU3R1ZGVudHN9XG4gICAgICAgICAgICAgIG9uU2VsZWN0PXtzZXRTZWxlY3RlZERlcGFydG1lbnR9XG4gICAgICAgICAgICAvPlxuICAgICAgICAgICkgOiAhc2VsZWN0ZWRQYXNzb3V0WWVhciA/IChcbiAgICAgICAgICAgIDxQYXNzb3V0WWVhckNhcmRzXG4gICAgICAgICAgICAgIGRlcGFydG1lbnRMYWJlbD17ZGVwYXJ0bWVudE9wdGlvbnMuZmluZChkID0+IGQudmFsdWUgPT09IHNlbGVjdGVkRGVwYXJ0bWVudCk/LmxhYmVsfVxuICAgICAgICAgICAgICBvbkJhY2s9e2hhbmRsZUJhY2tUb0RlcGFydG1lbnRzfVxuICAgICAgICAgICAgICBnZXRBdmFpbGFibGVQYXNzb3V0WWVhcnM9e2dldEF2YWlsYWJsZVBhc3NvdXRZZWFyc31cbiAgICAgICAgICAgICAgYWxsU3R1ZGVudHM9e2FsbFN0dWRlbnRzfVxuICAgICAgICAgICAgICBzZWxlY3RlZERlcGFydG1lbnQ9e3NlbGVjdGVkRGVwYXJ0bWVudH1cbiAgICAgICAgICAgICAgb25TZWxlY3RZZWFyPXtzZXRTZWxlY3RlZFBhc3NvdXRZZWFyfVxuICAgICAgICAgICAgLz5cbiAgICAgICAgICApIDogKFxuICAgICAgICAgICAgPFN0dWRlbnRMaXN0XG4gICAgICAgICAgICAgIGRlcGFydG1lbnRMYWJlbD17ZGVwYXJ0bWVudE9wdGlvbnMuZmluZChkID0+IGQudmFsdWUgPT09IHNlbGVjdGVkRGVwYXJ0bWVudCk/LmxhYmVsfVxuICAgICAgICAgICAgICBwYXNzb3V0WWVhcj17c2VsZWN0ZWRQYXNzb3V0WWVhcn1cbiAgICAgICAgICAgICAgb25CYWNrPXsoKSA9PiBzZXRTZWxlY3RlZFBhc3NvdXRZZWFyKG51bGwpfVxuICAgICAgICAgICAgICBzZWFyY2hUZXJtPXtzZWFyY2hUZXJtfVxuICAgICAgICAgICAgICBoYW5kbGVTZWFyY2hJbnB1dENoYW5nZT17aGFuZGxlU2VhcmNoSW5wdXRDaGFuZ2V9XG4gICAgICAgICAgICAgIGhhbmRsZVNlYXJjaEtleURvd249e2hhbmRsZVNlYXJjaEtleURvd259XG4gICAgICAgICAgICAgIGNncGFNaW49e2NncGFNaW59XG4gICAgICAgICAgICAgIHNldENncGFNaW49e3NldENncGFNaW59XG4gICAgICAgICAgICAgIGNncGFNYXg9e2NncGFNYXh9XG4gICAgICAgICAgICAgIHNldENncGFNYXg9e3NldENncGFNYXh9XG4gICAgICAgICAgICAgIGhhbmRsZVNlYXJjaD17aGFuZGxlU2VhcmNofVxuICAgICAgICAgICAgICBnZXRGaWx0ZXJlZFN0dWRlbnRzPXtnZXRGaWx0ZXJlZFN0dWRlbnRzfVxuICAgICAgICAgICAgICBjdXJyZW50UGFnZT17Y3VycmVudFBhZ2V9XG4gICAgICAgICAgICAgIGhhbmRsZVBhZ2VDaGFuZ2U9e2hhbmRsZVBhZ2VDaGFuZ2V9XG4gICAgICAgICAgICAgIGhhbmRsZVN0dWRlbnRDbGljaz17aGFuZGxlU3R1ZGVudENsaWNrfVxuICAgICAgICAgICAgICBsb2FkaW5nPXtsb2FkaW5nfVxuICAgICAgICAgICAgLz5cbiAgICAgICAgICApfVxuICAgICAgICA8Lz5cbiAgICAgICkgOiAoXG4gICAgICAgIDxTdHVkZW50UHJvZmlsZVxuICAgICAgICAgIHNlbGVjdGVkU3R1ZGVudD17c2VsZWN0ZWRTdHVkZW50fVxuICAgICAgICAgIGVkaXRlZFN0dWRlbnQ9e2VkaXRlZFN0dWRlbnR9XG4gICAgICAgICAgaXNFZGl0aW5nPXtpc0VkaXRpbmd9XG4gICAgICAgICAgaGFuZGxlQmFja1RvTGlzdD17aGFuZGxlQmFja1RvTGlzdH1cbiAgICAgICAgICBoYW5kbGVFZGl0PXtoYW5kbGVFZGl0fVxuICAgICAgICAgIGhhbmRsZVNhdmU9e2hhbmRsZVNhdmV9XG4gICAgICAgICAgaGFuZGxlQ2FuY2VsPXtoYW5kbGVDYW5jZWx9XG4gICAgICAgICAgaGFuZGxlSW5wdXRDaGFuZ2U9e2hhbmRsZUlucHV0Q2hhbmdlfVxuICAgICAgICAgIGRlcGFydG1lbnRPcHRpb25zPXtkZXBhcnRtZW50T3B0aW9uc31cbiAgICAgICAgLz5cbiAgICAgICl9XG4gICAgPC9kaXY+XG4gICk7XG59Il0sIm5hbWVzIjpbIkFycm93TGVmdCIsIkNhbGVuZGFyIiwiUmVmcmVzaEN3IiwiU2F2ZSIsIlNlYXJjaCIsIlVzZXIiLCJYIiwiR3JhZHVhdGlvbkNhcCIsInVzZUVmZmVjdCIsInVzZVJlZiIsInVzZVN0YXRlIiwic3R1ZGVudHNBUEkiLCJnZXRBdXRoVG9rZW4iLCJDdXN0b21Ecm9wZG93biIsIlN0dWRlbnRQcm9maWxlIiwiRGVwYXJ0bWVudENhcmRzIiwiUGFzc291dFllYXJDYXJkcyIsIlN0dWRlbnRMaXN0IiwiU3R1ZGVudE1hbmFnZW1lbnQiLCJkZXBhcnRtZW50T3B0aW9ucyIsInNlYXJjaFRlcm0iLCJzZXRTZWFyY2hUZXJtIiwiZGVib3VuY2VkU2VhcmNoVGVybSIsInNldERlYm91bmNlZFNlYXJjaFRlcm0iLCJzZWxlY3RlZERlcGFydG1lbnQiLCJzZXRTZWxlY3RlZERlcGFydG1lbnQiLCJzZWxlY3RlZFllYXIiLCJzZXRTZWxlY3RlZFllYXIiLCJzZWxlY3RlZFN0dWRlbnQiLCJzZXRTZWxlY3RlZFN0dWRlbnQiLCJpc0VkaXRpbmciLCJzZXRJc0VkaXRpbmciLCJlZGl0ZWRTdHVkZW50Iiwic2V0RWRpdGVkU3R1ZGVudCIsImRyb3Bkb3duUmVmIiwiaXNZZWFyRHJvcGRvd25PcGVuIiwic2V0SXNZZWFyRHJvcGRvd25PcGVuIiwic3R1ZGVudHMiLCJzZXRTdHVkZW50cyIsImFsbFN0dWRlbnRzIiwic2V0QWxsU3R1ZGVudHMiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsImVycm9yIiwic2V0RXJyb3IiLCJpc1JldHJ5aW5nIiwic2V0SXNSZXRyeWluZyIsImN1cnJlbnRQYWdlIiwic2V0Q3VycmVudFBhZ2UiLCJ0b3RhbFBhZ2VzIiwic2V0VG90YWxQYWdlcyIsInRvdGFsU3R1ZGVudHMiLCJzZXRUb3RhbFN0dWRlbnRzIiwiYXZhaWxhYmxlWWVhcnMiLCJzZXRBdmFpbGFibGVZZWFycyIsImRlcGFydG1lbnRTdGF0cyIsInNldERlcGFydG1lbnRTdGF0cyIsInNlbGVjdGVkUGFzc291dFllYXIiLCJzZXRTZWxlY3RlZFBhc3NvdXRZZWFyIiwiY2dwYU1pbiIsInNldENncGFNaW4iLCJjZ3BhTWF4Iiwic2V0Q2dwYU1heCIsInZhbHVlIiwibGFiZWwiLCJmZXRjaEFsbFN0dWRlbnRzIiwidG9rZW4iLCJFcnJvciIsImFsbERhdGEiLCJwYWdlIiwiaGFzTW9yZSIsInJlc3BvbnNlIiwiZ2V0U3R1ZGVudHMiLCJwYWdlX3NpemUiLCJkYXRhIiwicGFnaW5hdGlvbiIsInRvdGFsX3BhZ2VzIiwiZ2V0RGVmYXVsdExvY2F0aW9uIiwiaW5kZXgiLCJsb2NhdGlvbnMiLCJsZW5ndGgiLCJnZXREZWZhdWx0U3BlY2lhbGl6YXRpb24iLCJpc0NsYXNzMTIiLCJzcGVjaWFsaXphdGlvbnMiLCJzdHVkZW50c0RhdGEiLCJtYXAiLCJzdHVkZW50IiwiaWQiLCJyb2xsTnVtYmVyIiwic3R1ZGVudF9pZCIsIm5hbWUiLCJmaXJzdF9uYW1lIiwibGFzdF9uYW1lIiwidHJpbSIsImVtYWlsIiwiY29udGFjdF9lbWFpbCIsInBob25lIiwiZGVwYXJ0bWVudCIsImJyYW5jaCIsInllYXIiLCJnZXRZZWFyRnJvbUJyYW5jaCIsImNncGEiLCJncGEiLCJhZGRyZXNzIiwiZGF0ZU9mQmlydGgiLCJkYXRlX29mX2JpcnRoIiwicGFyZW50Q29udGFjdCIsInBhcmVudF9jb250YWN0IiwiZWR1Y2F0aW9uIiwic2tpbGxzIiwiam9pbmluZ195ZWFyIiwiYWRtaXNzaW9uX3llYXIiLCJwYXNzb3V0X3llYXIiLCJncmFkdWF0aW9uX3llYXIiLCJ0d2VsZnRoX2NncGEiLCJjbGFzc18xMl9jZ3BhIiwidHdlbGZ0aF9wZXJjZW50YWdlIiwiY2xhc3NfMTJfcGVyY2VudGFnZSIsInR3ZWxmdGhfeWVhcl9vZl9wYXNzaW5nIiwiY2xhc3NfMTJfeWVhciIsInR3ZWxmdGhfc2Nob29sIiwiY2xhc3NfMTJfc2Nob29sIiwidHdlbGZ0aF9ib2FyZCIsImNsYXNzXzEyX2JvYXJkIiwidHdlbGZ0aF9sb2NhdGlvbiIsImNsYXNzXzEyX2xvY2F0aW9uIiwidHdlbGZ0aF9zcGVjaWFsaXphdGlvbiIsImNsYXNzXzEyX3N0cmVhbSIsInRlbnRoX2NncGEiLCJjbGFzc18xMF9jZ3BhIiwidGVudGhfcGVyY2VudGFnZSIsImNsYXNzXzEwX3BlcmNlbnRhZ2UiLCJ0ZW50aF95ZWFyX29mX3Bhc3NpbmciLCJjbGFzc18xMF95ZWFyIiwidGVudGhfc2Nob29sIiwiY2xhc3NfMTBfc2Nob29sIiwidGVudGhfYm9hcmQiLCJjbGFzc18xMF9ib2FyZCIsInRlbnRoX2xvY2F0aW9uIiwiY2xhc3NfMTBfbG9jYXRpb24iLCJ0ZW50aF9zcGVjaWFsaXphdGlvbiIsImNsYXNzXzEwX3N0cmVhbSIsImNpdHkiLCJkaXN0cmljdCIsInN0YXRlIiwicGluY29kZSIsInBpbl9jb2RlIiwiY291bnRyeSIsInRlbnRoX2NlcnRpZmljYXRlIiwiY2xhc3NfMTBfY2VydGlmaWNhdGUiLCJ0d2VsZnRoX2NlcnRpZmljYXRlIiwiY2xhc3NfMTJfY2VydGlmaWNhdGUiLCJ0ZW50aF9jZXJ0aWZpY2F0ZV91cmwiLCJjbGFzc18xMF9jZXJ0aWZpY2F0ZV91cmwiLCJ0d2VsZnRoX2NlcnRpZmljYXRlX3VybCIsImNsYXNzXzEyX2NlcnRpZmljYXRlX3VybCIsInJlc3VtZSIsInJlc3VtZV91cmwiLCJzZW1lc3Rlcl9jZ3BhcyIsInNlbWVzdGVyIiwiZ2V0QXZhaWxhYmxlWWVhcnMiLCJnZXREZXBhcnRtZW50U3RhdHMiLCJlcnIiLCJjb25zb2xlIiwidGltZXIiLCJzZXRUaW1lb3V0IiwiY2xlYXJUaW1lb3V0IiwiZmV0Y2hTdHVkZW50cyIsImZpbHRlcmVkRGF0YSIsImZpbHRlciIsInNlYXJjaExvd2VyIiwidG9Mb3dlckNhc2UiLCJpbmNsdWRlcyIsInN0dWRlbnRzUGVyUGFnZSIsInN0YXJ0SW5kZXgiLCJlbmRJbmRleCIsInBhZ2luYXRlZFN0dWRlbnRzIiwic2xpY2UiLCJNYXRoIiwiY2VpbCIsInN0YXR1cyIsIm1lc3NhZ2UiLCJnZXRZZWFyRnJvbVN0dWRlbnRJZCIsInN0dWRlbnRJZCIsInllYXJQYXJ0Iiwic3Vic3RyaW5nIiwiaXNOYU4iLCJEYXRlIiwiZ2V0RnVsbFllYXIiLCJwYXJzZUludCIsImhhbmRsZUNsaWNrT3V0c2lkZSIsImV2ZW50IiwiY3VycmVudCIsImNvbnRhaW5zIiwidGFyZ2V0IiwiZG9jdW1lbnQiLCJhZGRFdmVudExpc3RlbmVyIiwicmVtb3ZlRXZlbnRMaXN0ZW5lciIsInllYXJzIiwiU2V0Iiwic29ydCIsInN0YXRzIiwiZm9yRWFjaCIsIk9iamVjdCIsImVudHJpZXMiLCJjb3VudCIsImdldEF2YWlsYWJsZVBhc3NvdXRZZWFycyIsInMiLCJwYXJ0cyIsInNwbGl0IiwieSIsIk51bWJlciIsIkFycmF5IiwiZnJvbSIsImEiLCJiIiwiZ2V0RmlsdGVyZWRTdHVkZW50cyIsImZpbHRlcmVkIiwiU3RyaW5nIiwicGFyc2VGbG9hdCIsImZpbHRlcmVkU3R1ZGVudHMiLCJoYW5kbGVTdHVkZW50Q2xpY2siLCJoYW5kbGVCYWNrVG9MaXN0IiwiaGFuZGxlQmFja1RvRGVwYXJ0bWVudHMiLCJoYW5kbGVFZGl0IiwiaGFuZGxlU2F2ZSIsInByZXYiLCJoYW5kbGVDYW5jZWwiLCJoYW5kbGVJbnB1dENoYW5nZSIsImZpZWxkIiwiaGFuZGxlUmV0cnkiLCJkZWJ1Z0JhY2tlbmQiLCJ3aW5kb3ciLCJvcGVuIiwiaGFuZGxlU2VhcmNoIiwiaGFuZGxlUGFnZUNoYW5nZSIsIm5ld1BhZ2UiLCJoYW5kbGVTZWFyY2hJbnB1dENoYW5nZSIsImUiLCJoYW5kbGVTZWFyY2hLZXlEb3duIiwia2V5IiwicHJldmVudERlZmF1bHQiLCJkaXYiLCJjbGFzc05hbWUiLCJwIiwidWwiLCJsaSIsImJ1dHRvbiIsIm9uQ2xpY2siLCJkaXNhYmxlZCIsImxvY2F0aW9uIiwiaHJlZiIsIm9uU2VsZWN0IiwiZGVwYXJ0bWVudExhYmVsIiwiZmluZCIsImQiLCJvbkJhY2siLCJvblNlbGVjdFllYXIiLCJwYXNzb3V0WWVhciJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/student-management/page.jsx\n"));

/***/ })

});